#!/usr/bin/env python3
"""
Quick start script for CodeRank API Server
"""

import subprocess
import sys
import os

def check_requirements():
    """Check if required packages are installed"""
    required_packages = ['flask', 'flask_cors', 'requests', 'bs4', 'rich']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   • {package}")
        print("\n📦 Install missing packages with:")
        print("   pip install -r requirements.txt")
        print("\n   Or install individually:")
        print("   pip install flask flask-cors requests beautifulsoup4 rich")
        return False
    
    return True

def main():
    """Main function to start the server"""
    print("🚀 CodeRank API Server Startup")
    print("=" * 40)
    
    # Check if requirements are met
    if not check_requirements():
        sys.exit(1)
    
    print("✅ All requirements satisfied")
    print("🌐 Starting API server...")
    
    try:
        # Start the API server
        from api_server import main as start_server
        start_server()
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
