import requests
from bs4 import BeautifulSoup
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint
import json
import re
from datetime import datetime

console = Console()

def calculate_highest_streak(submission_calendar):
    """Calculate the highest streak from submission calendar data"""
    if not submission_calendar:
        return 0
    
    # Convert timestamps to sorted list of dates
    timestamps = sorted([int(ts) for ts in submission_calendar.keys()])
    
    if not timestamps:
        return 0
    
    # Convert timestamps to days (remove time component)
    days = []
    for ts in timestamps:
        day = datetime.fromtimestamp(ts).date()
        if day not in days:
            days.append(day)
    
    days.sort()
    
    if len(days) <= 1:
        return len(days)
    
    max_streak = 1
    current_streak = 1
    
    for i in range(1, len(days)):
        # Check if consecutive days
        if (days[i] - days[i-1]).days == 1:
            current_streak += 1
            max_streak = max(max_streak, current_streak)
        else:
            current_streak = 1
    
    return max_streak

def get_leetcode_stats(profile_url):
    """Fetch comprehensive LeetCode statistics using GraphQL API"""
    try:
        # Extract username from URL
        username = profile_url.rstrip('/').split('/')[-1]
        
        # Updated GraphQL query that works with current LeetCode API
        query = """
        query userProfile($username: String!) {
            matchedUser(username: $username) {
                submitStats {
                    acSubmissionNum {
                        difficulty
                        count
                    }
                }
                badges {
                    id
                    displayName
                }
                userCalendar {
                    streak
                    totalActiveDays
                    submissionCalendar
                }
            }
        }
        """
        
        variables = {"username": username}
        
        response = requests.post(
            'https://leetcode.com/graphql',
            json={'query': query, 'variables': variables},
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        
        if response.status_code != 200:
            raise Exception(f"LeetCode API request failed: {response.status_code}")
        
        data = response.json()
        
        if 'errors' in data:
            raise Exception(f"GraphQL errors: {data['errors']}")
        
        user_data = data['data']['matchedUser']
        
        if not user_data:
            raise Exception("User not found or profile is private")
        
        # Parse submission stats
        solved_problems = {'easy': 0, 'medium': 0, 'hard': 0, 'total': 0}
        
        if user_data['submitStats'] and user_data['submitStats']['acSubmissionNum']:
            for stat in user_data['submitStats']['acSubmissionNum']:
                difficulty = stat['difficulty'].lower()
                count = stat['count']
                solved_problems[difficulty] = count
                solved_problems['total'] += count
        
        # Parse badges
        badges = []
        if user_data['badges']:
            badges = [badge['displayName'] for badge in user_data['badges']]
        
        # Parse calendar data
        calendar_data = user_data['userCalendar']
        current_streak = calendar_data['streak'] if calendar_data else 0
        total_active_days = calendar_data['totalActiveDays'] if calendar_data else 0
        
        # Calculate highest streak from submission calendar
        highest_streak = 0
        if calendar_data and calendar_data['submissionCalendar']:
            submission_calendar = json.loads(calendar_data['submissionCalendar'])
            highest_streak = calculate_highest_streak(submission_calendar)
        
        return {
            'platform': 'LeetCode',
            'username': username,
            'solved_problems': solved_problems,
            'badges': badges,
            'streak_info': {
                'current_streak': current_streak,
                'highest_streak': highest_streak,
                'total_active_days': total_active_days
            }
        }
        
    except Exception as e:
        return {'error': f"Failed to fetch LeetCode stats: {str(e)}"}

def get_hackerrank_stats(profile_url):
    """Scrape HackerRank profile for badges and scores"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(profile_url, headers=headers)
        
        if response.status_code != 200:
            raise Exception(f"Failed to access HackerRank profile: {response.status_code}")
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract username from URL
        username = profile_url.rstrip('/').split('/')[-1]
        
        # Extract badges with improved parsing
        badges = []
        
        # Multiple selectors to find badges
        badge_selectors = [
            '.badge-title',
            '.badge-name',
            '[class*="badge"]',
            '.achievement-badge',
            '.skill-badge',
            '.hacker-badge'
        ]
        
        for selector in badge_selectors:
            badge_elements = soup.select(selector)
            for badge in badge_elements:
                badge_text = badge.get_text(strip=True)
                if badge_text and 3 < len(badge_text) < 50 and badge_text not in badges:
                    # Filter out common non-badge text
                    if not any(word in badge_text.lower() for word in ['profile', 'view', 'edit', 'settings', 'menu']):
                        badges.append(badge_text)
        
        # Look for badges in text content
        text_content = soup.get_text()
        badge_patterns = [
            r'(\w+)\s+Badge',
            r'Badge:\s*(\w+)',
            r'Earned\s+(\w+)\s+badge'
        ]
        
        for pattern in badge_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            for match in matches:
                if match not in badges and len(match) > 2:
                    badges.append(match)
        
        # Extract scores and domains
        scores = {}
        
        # Look for score-related elements
        score_patterns = [
            r'(\w+(?:\s+\w+)*)\s*:\s*(\d+(?:\.\d+)?)\s*(?:points?|pts?)',
            r'(\w+(?:\s+\w+)*)\s+(\d+(?:\.\d+)?)\s*(?:points?|pts?)',
            r'Score:\s*(\d+(?:\.\d+)?)'
        ]
        
        page_text = soup.get_text()
        for pattern in score_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    domain, score = match
                    if any(keyword in domain.lower() for keyword in ['algorithm', 'python', 'sql', 'java', 'problem', 'data']):
                        scores[domain.strip()] = f"{score} points"
                else:
                    scores['Overall Score'] = f"{match} points"
        
        # If no specific scores found, look for any numerical values near domain keywords
        if not scores:
            domain_keywords = ['algorithms', 'python', 'sql', 'java', 'problem solving', 'data structures']
            for keyword in domain_keywords:
                if keyword in page_text.lower():
                    scores[keyword.title()] = "Available (check profile)"
        
        return {
            'platform': 'HackerRank',
            'username': username,
            'badges': badges[:15] if badges else ['No badges found - profile may be private'],
            'scores': scores if scores else {'Status': 'No scores found - profile may be private'},
            'streak_info': 'Not Available (HackerRank does not provide public streak data)'
        }
        
    except Exception as e:
        return {'error': f"Failed to fetch HackerRank stats: {str(e)}"}

def display_leetcode_stats(stats):
    """Display LeetCode statistics in a formatted table"""
    if 'error' in stats:
        console.print(f"[red]❌ {stats['error']}[/red]")
        return
    
    # Create problems table
    problems_table = Table(title=f"🟢 LeetCode Stats - {stats['username']}")
    problems_table.add_column("Difficulty", style="cyan", justify="left")
    problems_table.add_column("Count", style="magenta", justify="right")
    
    problems_table.add_row("Easy", str(stats['solved_problems']['easy']))
    problems_table.add_row("Medium", str(stats['solved_problems']['medium']))
    problems_table.add_row("Hard", str(stats['solved_problems']['hard']))
    problems_table.add_row("Total", str(stats['solved_problems']['total']), style="bold green")
    
    console.print(problems_table)
    
    # Display streak info
    streak_info = stats['streak_info']
    streak_panel = Panel(
        f"🔥 Current Streak: {streak_info['current_streak']} days\n"
        f"🏆 Highest Streak: {streak_info['highest_streak']} days\n"
        f"📅 Total Active Days: {streak_info['total_active_days']} days",
        title="Streak Information",
        border_style="green"
    )
    console.print(streak_panel)
    
    # Display badges
    if stats['badges']:
        badges_text = ", ".join(stats['badges'][:5])  # Show first 5 badges
        if len(stats['badges']) > 5:
            badges_text += f" ... and {len(stats['badges']) - 5} more"
        
        badges_panel = Panel(badges_text, title=f"🏅 Badges ({len(stats['badges'])} total)", border_style="yellow")
        console.print(badges_panel)
    else:
        console.print("[yellow]🏅 No badges found[/yellow]")

def display_hackerrank_stats(stats):
    """Display HackerRank statistics in a formatted table"""
    if 'error' in stats:
        console.print(f"[red]❌ {stats['error']}[/red]")
        return
    
    console.print(f"\n🟠 HackerRank Stats - {stats['username']}")
    
    # Display badges
    if stats['badges'] and stats['badges'][0] != 'No badges found - profile may be private':
        # Clean and format badges
        clean_badges = []
        for badge in stats['badges']:
            if badge and len(badge.strip()) > 2:
                clean_badges.append(badge.strip())
        
        if clean_badges:
            badges_text = ", ".join(clean_badges[:8])  # Show first 8 badges
            if len(clean_badges) > 8:
                badges_text += f" ... and {len(clean_badges) - 8} more"
            
            badges_panel = Panel(badges_text, title=f"🏅 Badges ({len(clean_badges)} found)", border_style="orange1")
            console.print(badges_panel)
        else:
            console.print("[yellow]🏅 No clear badges found[/yellow]")
    else:
        console.print("[yellow]🏅 No badges found - profile may be private[/yellow]")
    
    # Display scores
    if stats['scores'] and 'No scores found' not in str(stats['scores']):
        scores_table = Table(title="📊 Domain Scores")
        scores_table.add_column("Domain", style="cyan", justify="left")
        scores_table.add_column("Score/Status", style="green", justify="right")
        
        for domain, score in stats['scores'].items():
            scores_table.add_row(domain, str(score))
        
        console.print(scores_table)
    else:
        console.print("[yellow]📊 No scores found - profile may be private[/yellow]")
    
    # Streak info
    console.print(f"[yellow]ℹ️  Streak Info: {stats['streak_info']}[/yellow]")

def main():
    """Main function to run the coding stats tool"""
    console.print(Panel.fit("📊 Coding Stats Tool - LeetCode & HackerRank", style="bold blue"))
    
    # Get user input
    leetcode_url = console.input("\n🟢 Enter your LeetCode profile URL: ").strip()
    hackerrank_url = console.input("🟠 Enter your HackerRank profile URL: ").strip()
    
    console.print("\n[yellow]⏳ Fetching statistics...[/yellow]")
    
    # Fetch and display LeetCode stats
    console.print("\n" + "="*50)
    leetcode_stats = get_leetcode_stats(leetcode_url)
    display_leetcode_stats(leetcode_stats)
    
    # Fetch and display HackerRank stats
    console.print("\n" + "="*50)
    hackerrank_stats = get_hackerrank_stats(hackerrank_url)
    display_hackerrank_stats(hackerrank_stats)
    
    console.print("\n[green]✅ Stats retrieval completed![/green]")

if __name__ == "__main__":
    main()