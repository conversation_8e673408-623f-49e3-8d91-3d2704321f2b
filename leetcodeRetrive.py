import requests
from bs4 import BeautifulSoup
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint
import json
import re
from datetime import datetime

console = Console()

def calculate_highest_streak(submission_calendar):
    """Calculate the highest streak from submission calendar data"""
    if not submission_calendar:
        return 0
    
    # Convert timestamps to sorted list of dates
    timestamps = sorted([int(ts) for ts in submission_calendar.keys()])
    
    if not timestamps:
        return 0
    
    # Convert timestamps to days (remove time component)
    days = []
    for ts in timestamps:
        day = datetime.fromtimestamp(ts).date()
        if day not in days:
            days.append(day)
    
    days.sort()
    
    if len(days) <= 1:
        return len(days)
    
    max_streak = 1
    current_streak = 1
    
    for i in range(1, len(days)):
        # Check if consecutive days
        if (days[i] - days[i-1]).days == 1:
            current_streak += 1
            max_streak = max(max_streak, current_streak)
        else:
            current_streak = 1
    
    return max_streak

def get_leetcode_stats(profile_url):
    """Fetch comprehensive LeetCode statistics using GraphQL API"""
    try:
        # Extract username from URL
        username = profile_url.rstrip('/').split('/')[-1]
        
        # Updated GraphQL query that works with current LeetCode API
        query = """
        query userProfile($username: String!) {
            matchedUser(username: $username) {
                submitStats {
                    acSubmissionNum {
                        difficulty
                        count
                    }
                }
                badges {
                    id
                    displayName
                }
                userCalendar {
                    streak
                    totalActiveDays
                    submissionCalendar
                }
            }
        }
        """
        
        variables = {"username": username}
        
        response = requests.post(
            'https://leetcode.com/graphql',
            json={'query': query, 'variables': variables},
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )
        
        if response.status_code != 200:
            raise Exception(f"LeetCode API request failed: {response.status_code}")
        
        data = response.json()
        
        if 'errors' in data:
            raise Exception(f"GraphQL errors: {data['errors']}")
        
        user_data = data['data']['matchedUser']
        
        if not user_data:
            raise Exception("User not found or profile is private")
        
        # Parse submission stats
        solved_problems = {'easy': 0, 'medium': 0, 'hard': 0, 'total': 0}
        
        if user_data['submitStats'] and user_data['submitStats']['acSubmissionNum']:
            for stat in user_data['submitStats']['acSubmissionNum']:
                difficulty = stat['difficulty'].lower()
                count = stat['count']
                solved_problems[difficulty] = count
                solved_problems['total'] += count
        
        # Parse badges
        badges = []
        if user_data['badges']:
            badges = [badge['displayName'] for badge in user_data['badges']]
        
        # Parse calendar data
        calendar_data = user_data['userCalendar']
        current_streak = calendar_data['streak'] if calendar_data else 0
        total_active_days = calendar_data['totalActiveDays'] if calendar_data else 0
        
        # Calculate highest streak from submission calendar
        highest_streak = 0
        if calendar_data and calendar_data['submissionCalendar']:
            submission_calendar = json.loads(calendar_data['submissionCalendar'])
            highest_streak = calculate_highest_streak(submission_calendar)
        
        return {
            'platform': 'LeetCode',
            'username': username,
            'solved_problems': solved_problems,
            'badges': badges,
            'streak_info': {
                'current_streak': current_streak,
                'highest_streak': highest_streak,
                'total_active_days': total_active_days
            }
        }
        
    except Exception as e:
        return {'error': f"Failed to fetch LeetCode stats: {str(e)}"}

def get_total_problems_solved(username, platform='leetcode'):
    """
    Get total number of problems solved for a user

    Args:
        username (str): Username on the platform
        platform (str): 'leetcode' or 'hackerrank'

    Returns:
        dict: Contains total problems and breakdown by difficulty
    """
    if platform.lower() == 'leetcode':
        profile_url = f"https://leetcode.com/{username}/"
        stats = get_leetcode_stats(profile_url)
        if 'error' not in stats:
            return {
                'total': stats['solved_problems']['total'],
                'easy': stats['solved_problems']['easy'],
                'medium': stats['solved_problems']['medium'],
                'hard': stats['solved_problems']['hard'],
                'platform': 'LeetCode'
            }
        else:
            return {'error': stats['error']}

    elif platform.lower() == 'hackerrank':
        # HackerRank doesn't provide problem count in the same way
        # We'll return available data
        profile_url = f"https://www.hackerrank.com/{username}"
        stats = get_hackerrank_stats(profile_url)
        if 'error' not in stats:
            return {
                'total': 'Not Available',
                'badges_count': len(stats['badges']) if stats['badges'] else 0,
                'domains': list(stats['scores'].keys()) if stats['scores'] else [],
                'platform': 'HackerRank'
            }
        else:
            return {'error': stats['error']}

    else:
        return {'error': 'Unsupported platform. Use "leetcode" or "hackerrank"'}

def get_contest_rating(username, platform='leetcode'):
    """
    Get contest rating for a user (LeetCode only)

    Args:
        username (str): LeetCode username
        platform (str): Platform name

    Returns:
        dict: Contest rating information
    """
    if platform.lower() != 'leetcode':
        return {'error': 'Contest rating only available for LeetCode'}

    try:
        # GraphQL query for contest rating
        query = """
        query userContestRanking($username: String!) {
            userContestRanking(username: $username) {
                rating
                globalRanking
                totalParticipants
                topPercentage
                badge {
                    name
                }
            }
        }
        """

        variables = {"username": username}

        response = requests.post(
            'https://leetcode.com/graphql',
            json={'query': query, 'variables': variables},
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        )

        if response.status_code == 200:
            data = response.json()
            if 'errors' not in data and data['data']['userContestRanking']:
                contest_data = data['data']['userContestRanking']
                return {
                    'rating': contest_data.get('rating', 0),
                    'global_ranking': contest_data.get('globalRanking', 0),
                    'total_participants': contest_data.get('totalParticipants', 0),
                    'top_percentage': contest_data.get('topPercentage', 0),
                    'badge': contest_data.get('badge', {}).get('name', 'No Badge') if contest_data.get('badge') else 'No Badge'
                }
            else:
                return {'rating': 0, 'message': 'No contest data available'}
        else:
            return {'error': 'Failed to fetch contest rating'}

    except Exception as e:
        return {'error': f"Failed to fetch contest rating: {str(e)}"}

def get_hackerrank_stats(profile_url):
    """
    Enhanced HackerRank data retrieval with better problem solving metrics
    Scrape HackerRank profile for badges, scores, and problem solving data
    """
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(profile_url, headers=headers)
        
        if response.status_code != 200:
            raise Exception(f"Failed to access HackerRank profile: {response.status_code}")
        
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract username from URL
        username = profile_url.rstrip('/').split('/')[-1]
        
        # Extract badges with improved parsing
        badges = []
        
        # Multiple selectors to find badges
        badge_selectors = [
            '.badge-title',
            '.badge-name',
            '[class*="badge"]',
            '.achievement-badge',
            '.skill-badge',
            '.hacker-badge'
        ]
        
        for selector in badge_selectors:
            badge_elements = soup.select(selector)
            for badge in badge_elements:
                badge_text = badge.get_text(strip=True)
                if badge_text and 3 < len(badge_text) < 50 and badge_text not in badges:
                    # Filter out common non-badge text
                    if not any(word in badge_text.lower() for word in ['profile', 'view', 'edit', 'settings', 'menu']):
                        badges.append(badge_text)
        
        # Look for badges in text content
        text_content = soup.get_text()
        badge_patterns = [
            r'(\w+)\s+Badge',
            r'Badge:\s*(\w+)',
            r'Earned\s+(\w+)\s+badge'
        ]
        
        for pattern in badge_patterns:
            matches = re.findall(pattern, text_content, re.IGNORECASE)
            for match in matches:
                if match not in badges and len(match) > 2:
                    badges.append(match)
        
        # Extract scores and domains
        scores = {}
        
        # Look for score-related elements
        score_patterns = [
            r'(\w+(?:\s+\w+)*)\s*:\s*(\d+(?:\.\d+)?)\s*(?:points?|pts?)',
            r'(\w+(?:\s+\w+)*)\s+(\d+(?:\.\d+)?)\s*(?:points?|pts?)',
            r'Score:\s*(\d+(?:\.\d+)?)'
        ]
        
        page_text = soup.get_text()
        for pattern in score_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    domain, score = match
                    if any(keyword in domain.lower() for keyword in ['algorithm', 'python', 'sql', 'java', 'problem', 'data']):
                        scores[domain.strip()] = f"{score} points"
                else:
                    scores['Overall Score'] = f"{match} points"
        
        # If no specific scores found, look for any numerical values near domain keywords
        if not scores:
            domain_keywords = ['algorithms', 'python', 'sql', 'java', 'problem solving', 'data structures']
            for keyword in domain_keywords:
                if keyword in page_text.lower():
                    scores[keyword.title()] = "Available (check profile)"
        
        return {
            'platform': 'HackerRank',
            'username': username,
            'badges': badges[:15] if badges else ['No badges found - profile may be private'],
            'scores': scores if scores else {'Status': 'No scores found - profile may be private'},
            'streak_info': 'Not Available (HackerRank does not provide public streak data)'
        }
        
    except Exception as e:
        return {'error': f"Failed to fetch HackerRank stats: {str(e)}"}

def display_leetcode_stats(stats):
    """Display LeetCode statistics in a formatted table"""
    if 'error' in stats:
        console.print(f"[red]❌ {stats['error']}[/red]")
        return
    
    # Create problems table
    problems_table = Table(title=f"🟢 LeetCode Stats - {stats['username']}")
    problems_table.add_column("Difficulty", style="cyan", justify="left")
    problems_table.add_column("Count", style="magenta", justify="right")
    
    problems_table.add_row("Easy", str(stats['solved_problems']['easy']))
    problems_table.add_row("Medium", str(stats['solved_problems']['medium']))
    problems_table.add_row("Hard", str(stats['solved_problems']['hard']))
    problems_table.add_row("Total", str(stats['solved_problems']['total']), style="bold green")
    
    console.print(problems_table)
    
    # Display streak info
    streak_info = stats['streak_info']
    streak_panel = Panel(
        f"🔥 Current Streak: {streak_info['current_streak']} days\n"
        f"🏆 Highest Streak: {streak_info['highest_streak']} days\n"
        f"📅 Total Active Days: {streak_info['total_active_days']} days",
        title="Streak Information",
        border_style="green"
    )
    console.print(streak_panel)
    
    # Display badges
    if stats['badges']:
        badges_text = ", ".join(stats['badges'][:5])  # Show first 5 badges
        if len(stats['badges']) > 5:
            badges_text += f" ... and {len(stats['badges']) - 5} more"
        
        badges_panel = Panel(badges_text, title=f"🏅 Badges ({len(stats['badges'])} total)", border_style="yellow")
        console.print(badges_panel)
    else:
        console.print("[yellow]🏅 No badges found[/yellow]")

def display_hackerrank_stats(stats):
    """Display HackerRank statistics in a formatted table"""
    if 'error' in stats:
        console.print(f"[red]❌ {stats['error']}[/red]")
        return
    
    console.print(f"\n🟠 HackerRank Stats - {stats['username']}")
    
    # Display badges
    if stats['badges'] and stats['badges'][0] != 'No badges found - profile may be private':
        # Clean and format badges
        clean_badges = []
        for badge in stats['badges']:
            if badge and len(badge.strip()) > 2:
                clean_badges.append(badge.strip())
        
        if clean_badges:
            badges_text = ", ".join(clean_badges[:8])  # Show first 8 badges
            if len(clean_badges) > 8:
                badges_text += f" ... and {len(clean_badges) - 8} more"
            
            badges_panel = Panel(badges_text, title=f"🏅 Badges ({len(clean_badges)} found)", border_style="orange1")
            console.print(badges_panel)
        else:
            console.print("[yellow]🏅 No clear badges found[/yellow]")
    else:
        console.print("[yellow]🏅 No badges found - profile may be private[/yellow]")
    
    # Display scores
    if stats['scores'] and 'No scores found' not in str(stats['scores']):
        scores_table = Table(title="📊 Domain Scores")
        scores_table.add_column("Domain", style="cyan", justify="left")
        scores_table.add_column("Score/Status", style="green", justify="right")
        
        for domain, score in stats['scores'].items():
            scores_table.add_row(domain, str(score))
        
        console.print(scores_table)
    else:
        console.print("[yellow]📊 No scores found - profile may be private[/yellow]")
    
    # Streak info
    console.print(f"[yellow]ℹ️  Streak Info: {stats['streak_info']}[/yellow]")

def get_comprehensive_stats(username, platform='leetcode'):
    """
    Get comprehensive statistics for a user including problems, rating, and streaks

    Args:
        username (str): Username on the platform
        platform (str): 'leetcode' or 'hackerrank'

    Returns:
        dict: Comprehensive user statistics
    """
    if platform.lower() == 'leetcode':
        profile_url = f"https://leetcode.com/{username}/"
        stats = get_leetcode_stats(profile_url)

        if 'error' not in stats:
            # Get contest rating
            contest_rating = get_contest_rating(username, 'leetcode')

            return {
                'username': username,
                'platform': 'LeetCode',
                'problems_solved': stats['solved_problems'],
                'contest_rating': contest_rating.get('rating', 0),
                'global_ranking': contest_rating.get('global_ranking', 0),
                'current_streak': stats['streak_info']['current_streak'],
                'highest_streak': stats['streak_info']['highest_streak'],
                'total_active_days': stats['streak_info']['total_active_days'],
                'badges': stats['badges'],
                'last_updated': datetime.now().isoformat()
            }
        else:
            return {'error': stats['error']}

    elif platform.lower() == 'hackerrank':
        profile_url = f"https://www.hackerrank.com/{username}"
        stats = get_hackerrank_stats(profile_url)

        if 'error' not in stats:
            return {
                'username': username,
                'platform': 'HackerRank',
                'badges': stats['badges'],
                'scores': stats['scores'],
                'badges_count': len(stats['badges']) if stats['badges'] else 0,
                'last_updated': datetime.now().isoformat()
            }
        else:
            return {'error': stats['error']}

    else:
        return {'error': 'Unsupported platform. Use "leetcode" or "hackerrank"'}

def create_web_api():
    """
    Create a simple Flask web API for the CodeRank application
    """
    try:
        from flask import Flask, jsonify, request
        from flask_cors import CORS

        app = Flask(__name__)
        CORS(app)  # Enable CORS for web app integration

        @app.route('/api/leetcode/<username>', methods=['GET'])
        def get_leetcode_user_stats(username):
            """API endpoint to get LeetCode stats for a user"""
            try:
                stats = get_comprehensive_stats(username, 'leetcode')
                return jsonify(stats)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @app.route('/api/hackerrank/<username>', methods=['GET'])
        def get_hackerrank_user_stats(username):
            """API endpoint to get HackerRank stats for a user"""
            try:
                stats = get_comprehensive_stats(username, 'hackerrank')
                return jsonify(stats)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @app.route('/api/problems/<username>', methods=['GET'])
        def get_user_problems(username):
            """API endpoint to get total problems solved"""
            platform = request.args.get('platform', 'leetcode')
            try:
                problems = get_total_problems_solved(username, platform)
                return jsonify(problems)
            except Exception as e:
                return jsonify({'error': str(e)}), 500

        @app.route('/api/verify/<username>', methods=['GET'])
        def verify_username(username):
            """API endpoint to verify if a username exists"""
            platform = request.args.get('platform', 'leetcode')
            try:
                stats = get_comprehensive_stats(username, platform)
                if 'error' in stats:
                    return jsonify({'valid': False, 'error': stats['error']})
                else:
                    return jsonify({'valid': True, 'username': username, 'platform': platform})
            except Exception as e:
                return jsonify({'valid': False, 'error': str(e)})

        @app.route('/api/health', methods=['GET'])
        def health_check():
            """Health check endpoint"""
            return jsonify({'status': 'healthy', 'service': 'CodeRank Data API'})

        return app

    except ImportError:
        console.print("[red]❌ Flask not installed. Install with: pip install flask flask-cors[/red]")
        return None

def save_stats_to_json(username, platform='leetcode', filename=None):
    """
    Save user statistics to a JSON file

    Args:
        username (str): Username to fetch stats for
        platform (str): Platform name
        filename (str): Optional filename, defaults to username_platform.json
    """
    if not filename:
        filename = f"{username}_{platform}_stats.json"

    stats = get_comprehensive_stats(username, platform)

    try:
        with open(filename, 'w') as f:
            json.dump(stats, f, indent=2)
        console.print(f"[green]✅ Stats saved to {filename}[/green]")
        return filename
    except Exception as e:
        console.print(f"[red]❌ Failed to save stats: {str(e)}[/red]")
        return None

def main():
    """Main function to run the coding stats tool"""
    console.print(Panel.fit("📊 Coding Stats Tool - LeetCode & HackerRank", style="bold blue"))

    # Menu options
    console.print("\n[cyan]Choose an option:[/cyan]")
    console.print("1. 🟢 Get LeetCode stats")
    console.print("2. 🟠 Get HackerRank stats")
    console.print("3. 📊 Get comprehensive stats (both platforms)")
    console.print("4. 🌐 Start Web API server")
    console.print("5. 💾 Save stats to JSON file")

    choice = console.input("\nEnter your choice (1-5): ").strip()

    if choice == "1":
        leetcode_url = console.input("\n🟢 Enter your LeetCode profile URL: ").strip()
        console.print("\n[yellow]⏳ Fetching LeetCode statistics...[/yellow]")
        leetcode_stats = get_leetcode_stats(leetcode_url)
        display_leetcode_stats(leetcode_stats)

    elif choice == "2":
        hackerrank_url = console.input("\n🟠 Enter your HackerRank profile URL: ").strip()
        console.print("\n[yellow]⏳ Fetching HackerRank statistics...[/yellow]")
        hackerrank_stats = get_hackerrank_stats(hackerrank_url)
        display_hackerrank_stats(hackerrank_stats)

    elif choice == "3":
        username = console.input("\n👤 Enter your username: ").strip()
        platform = console.input("🔗 Enter platform (leetcode/hackerrank): ").strip().lower()

        console.print(f"\n[yellow]⏳ Fetching comprehensive {platform} statistics...[/yellow]")
        stats = get_comprehensive_stats(username, platform)

        if 'error' not in stats:
            console.print(f"\n[green]✅ Comprehensive stats for {username} on {platform.title()}:[/green]")
            console.print(json.dumps(stats, indent=2))
        else:
            console.print(f"[red]❌ {stats['error']}[/red]")

    elif choice == "4":
        console.print("\n[yellow]🌐 Starting Web API server...[/yellow]")
        app = create_web_api()
        if app:
            console.print("[green]✅ Web API server starting on http://localhost:5000[/green]")
            console.print("[cyan]Available endpoints:[/cyan]")
            console.print("  • GET /api/leetcode/<username>")
            console.print("  • GET /api/hackerrank/<username>")
            console.print("  • GET /api/problems/<username>?platform=leetcode")
            console.print("  • GET /api/verify/<username>?platform=leetcode")
            console.print("  • GET /api/health")
            app.run(debug=True, port=5000)

    elif choice == "5":
        username = console.input("\n👤 Enter username: ").strip()
        platform = console.input("🔗 Enter platform (leetcode/hackerrank): ").strip().lower()
        filename = console.input("📁 Enter filename (optional): ").strip()

        if not filename:
            filename = None

        console.print(f"\n[yellow]⏳ Saving {platform} stats for {username}...[/yellow]")
        saved_file = save_stats_to_json(username, platform, filename)

        if saved_file:
            console.print(f"[green]✅ Stats saved successfully![/green]")

    else:
        console.print("[red]❌ Invalid choice. Please run the script again.[/red]")

    console.print("\n[green]✅ Operation completed![/green]")

if __name__ == "__main__":
    main()