# 🔐 CodeRank Authentication Guide

## How to Use Sign In & Register Features

### 🎯 **Quick Start**

1. **Open the Application**: Open `index.html` in your browser
2. **Look for Auth Buttons**: You'll see "Sign In" and "Register" buttons in the top navigation
3. **Click to Test**: Click either button to open the authentication modals

### 📍 **Where to Find Authentication Buttons**

#### **Desktop Navigation (Top Right)**
- 🔵 **"Sign In"** button (blue)
- 🔴 **"Register"** button (red)

#### **Mobile Navigation (Hamburger Menu)**
- Tap the hamburger menu (☰) in top right
- Find "Sign In" and "Register" buttons at the bottom

#### **Hero Section**
- 🚀 **"Get Started"** button (opens Register modal)

#### **Call-to-Action Section**
- 🎯 **"Get Started"** button (opens Register modal)

### 🔐 **Sign In Modal Features**

When you click "Sign In", you'll see:

```
┌─────────────────────────────┐
│           Sign In           │
├─────────────────────────────┤
│ Email: [________________]   │
│ Password: [_____________]   │
│ ☐ Remember me               │
│ Forgot password?            │
│ [      Sign In      ]       │
└─────────────────────────────┘
```

**Features:**
- ✅ Email validation
- ✅ Password field
- ✅ Remember me checkbox
- ✅ Forgot password link
- ✅ Form validation

### 📝 **Register Modal Features**

When you click "Register", you'll see:

```
┌─────────────────────────────┐
│          Register           │
├─────────────────────────────┤
│ First Name: [_____] Last: [_____] │
│ Email: [________________]   │
│ Department: [▼ Computer Science]  │
│ Year: [▼ Junior]           │
│ LeetCode Username: [_______] │
│ Password: [_____________]   │
│ Confirm Password: [_____]   │
│ [      Register     ]       │
└─────────────────────────────┘
```

**Features:**
- ✅ Complete user profile setup
- ✅ Department selection (CS, Data Science, etc.)
- ✅ Academic year selection
- ✅ **LeetCode username integration**
- ✅ Password confirmation
- ✅ Form validation

### 🎮 **How to Test Authentication**

#### **Method 1: Use Test Page**
1. Open `test_auth.html` in your browser
2. Click the test buttons to verify each feature works

#### **Method 2: Use Main Application**
1. Open `index.html`
2. Look for the yellow demo banner at the top
3. Click any "Sign In" or "Register" button
4. Fill out the forms to test functionality

#### **Method 3: Use Debug Button**
1. Open `index.html`
2. Look for the purple "🧪 Test Auth" button in bottom-right corner
3. Click it to quickly test modals

### 🔗 **LeetCode Integration Testing**

#### **Register with Real LeetCode Username:**
1. Click "Register"
2. Fill in your details
3. **Important**: Enter your actual LeetCode username
4. Click "Register"
5. Go to your profile and click "Verify" to connect real data

#### **Test with Sample Usernames:**
- `lee215` (popular competitive programmer)
- `awice` (LeetCode staff)
- `votrubac` (active user)

### 🚀 **With API Server (Real Data)**

#### **Setup:**
```bash
# Install dependencies
pip install -r requirements.txt

# Start API server
python start_server.py
```

#### **Test Real Integration:**
1. Start the API server
2. Register with your real LeetCode username
3. Click "Verify" in profile settings
4. See your actual problem counts, ratings, and streaks!

### 🎯 **Expected Behavior**

#### **After Successful Registration:**
- ✅ Welcome notification appears
- ✅ "Welcome Badge" earned
- ✅ Navigation buttons change to show your name
- ✅ Profile section updates with your info
- ✅ User menu appears when clicking your name

#### **After Successful Sign In:**
- ✅ Success notification
- ✅ UI updates for logged-in state
- ✅ Access to profile editing
- ✅ Real-time data sync (if API server running)

### 🛠 **Troubleshooting**

#### **Buttons Not Working?**
1. Check browser console (F12) for errors
2. Ensure JavaScript is enabled
3. Try the test page: `test_auth.html`
4. Look for the debug button in bottom-right

#### **Modals Not Appearing?**
1. Check if popup blockers are disabled
2. Try clicking the purple debug button
3. Refresh the page and try again

#### **LeetCode Verification Failing?**
1. Ensure API server is running: `python start_server.py`
2. Check username spelling
3. Try with known usernames like `lee215`
4. Check browser console for API errors

### 📱 **Mobile Testing**

1. Open on mobile device or use browser dev tools
2. Tap hamburger menu (☰)
3. Scroll to bottom for auth buttons
4. Modals are responsive and touch-friendly

### 🎉 **Demo Data**

If API server is not running, the app uses simulated data:
- **Sample User**: John Doe, Computer Science Junior
- **Sample Stats**: 217 problems solved, 1432 rating
- **Sample Achievements**: Various coding badges

### 🔧 **Advanced Features**

#### **User Menu (After Login):**
- View Profile
- Edit Profile
- 🔄 Sync LeetCode Data (with API server)
- My Achievements
- Settings
- Sign Out

#### **Real-time Features:**
- Live leaderboard updates
- Achievement notifications
- Streak tracking
- Progress synchronization

---

## 🎯 **Quick Test Checklist**

- [ ] Click "Sign In" button → Modal opens
- [ ] Click "Register" button → Modal opens  
- [ ] Fill registration form → Success notification
- [ ] Click user name → User menu appears
- [ ] Edit profile → LeetCode verification works
- [ ] Check achievements → Badges display
- [ ] Test on mobile → Responsive design works

**🎉 All authentication features are fully implemented and ready to use!**
