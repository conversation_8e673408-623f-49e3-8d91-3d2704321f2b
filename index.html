<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeRank - College LeetCode Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #dc2626 0%, #2563eb 100%);
        }
        
        .streak-flame {
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .badge-pop {
            animation: pop 0.5s ease-out;
        }
        
        @keyframes pop {
            0% { transform: scale(0); }
            80% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #f00;
            opacity: 0;
        }

        /* Mobile responsive enhancements */
        @media (max-width: 640px) {
            .hero-title {
                font-size: 2.5rem;
                line-height: 1.1;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .leaderboard-table {
                font-size: 0.875rem;
            }

            .modal-content {
                margin: 1rem;
                max-width: calc(100vw - 2rem);
            }
        }

        /* Smooth transitions */
        * {
            transition: all 0.2s ease-in-out;
        }

        /* Interactive button effects */
        .btn-interactive:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-interactive:active {
            transform: translateY(0);
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-md sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-code text-red-600 text-2xl mr-2"></i>
                        <span class="text-xl font-bold text-blue-800">CodeRank</span>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="#leaderboard-section" class="border-blue-500 text-blue-800 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">Leaderboard</a>
                        <a href="#analytics-section" class="border-transparent text-gray-600 hover:border-gray-300 hover:text-gray-800 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">Analytics</a>
                        <a href="#profile-section" class="border-transparent text-gray-600 hover:border-gray-300 hover:text-gray-800 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">Profile</a>
                        <a href="#events-section" class="border-transparent text-gray-600 hover:border-gray-300 hover:text-gray-800 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">Events</a>
                    </div>
                </div>
                <div class="hidden sm:ml-6 sm:flex sm:items-center">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium mr-4">Sign In</button>
                    <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">Register</button>
                </div>
                <div class="-mr-2 flex items-center sm:hidden">
                    <button type="button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500" aria-controls="mobile-menu" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="sm:hidden hidden" id="mobile-menu">
            <div class="pt-2 pb-3 space-y-1">
                <a href="#leaderboard-section" class="bg-blue-50 border-blue-500 text-blue-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Leaderboard</a>
                <a href="#analytics-section" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Analytics</a>
                <a href="#profile-section" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Profile</a>
                <a href="#events-section" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Events</a>
                <div class="pt-4 pb-3 border-t border-gray-200">
                    <div class="flex items-center px-4">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium mr-4 w-full">Sign In</button>
                        <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium w-full">Register</button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div id="home-section" class="gradient-bg text-white">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="lg:grid lg:grid-cols-2 lg:gap-8 items-center">
                <div class="mb-8 lg:mb-0">
                    <h1 class="hero-title text-4xl font-extrabold tracking-tight sm:text-5xl lg:text-6xl mb-4">
                        Rank Your Coding Skills
                    </h1>
                    <p class="text-xl text-blue-100 max-w-3xl">
                        Track your LeetCode progress, compete with peers, and climb the college leaderboard. Join the coding revolution today!
                    </p>
                    <div class="mt-8 flex flex-col sm:flex-row gap-4">
                        <button class="btn-interactive bg-white text-blue-800 hover:bg-gray-100 px-6 py-3 rounded-md text-lg font-semibold shadow-lg transition duration-300">
                            Get Started <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                        <button class="btn-interactive border-2 border-white text-white hover:bg-white hover:text-blue-800 px-6 py-3 rounded-md text-lg font-semibold transition duration-300">
                            Learn More
                        </button>
                    </div>
                </div>
                <div class="relative">
                    <div class="bg-white rounded-xl shadow-2xl overflow-hidden">
                        <div class="bg-blue-800 px-4 py-3 flex items-center">
                            <div class="flex-1">
                                <h3 class="text-lg font-bold text-white">Top Performers</h3>
                            </div>
                            <div class="flex space-x-2">
                                <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded">Weekly</span>
                                <span class="bg-white text-blue-800 text-xs px-2 py-1 rounded">All Time</span>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="space-y-4">
                                <!-- Top Performer 1 -->
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                                        <span class="text-red-600 font-bold">1</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-800">Alex Johnson</h4>
                                        <p class="text-sm text-gray-600">Computer Science - Senior</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-blue-800">428</p>
                                        <p class="text-xs text-gray-500">problems solved</p>
                                    </div>
                                </div>
                                
                                <!-- Top Performer 2 -->
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                        <span class="text-blue-600 font-bold">2</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-800">Sarah Williams</h4>
                                        <p class="text-sm text-gray-600">Data Science - Junior</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-blue-800">396</p>
                                        <p class="text-xs text-gray-500">problems solved</p>
                                    </div>
                                </div>
                                
                                <!-- Top Performer 3 -->
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                                        <span class="text-gray-600 font-bold">3</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-800">Michael Chen</h4>
                                        <p class="text-sm text-gray-600">Computer Engineering - Sophomore</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-bold text-blue-800">372</p>
                                        <p class="text-xs text-gray-500">problems solved</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 text-center">
                                <button class="text-blue-800 font-medium hover:text-blue-600 flex items-center justify-center w-full">
                                    View Full Leaderboard <i class="fas fa-chevron-down ml-2"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div id="stats-section" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                College Coding Statistics
            </h2>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-4">
                See how our students are performing on LeetCode
            </p>
        </div>
        
        <div class="stats-grid grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <!-- Total Problems Solved -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                            <i class="fas fa-check-circle text-blue-600 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Problems Solved</dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900">12,847</div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-1 bg-blue-600 rounded-full" style="width: 72%"></div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">+2,341 this semester</p>
                    </div>
                </div>
            </div>
            
            <!-- Active Streaks -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
                            <i class="fas fa-fire text-red-600 text-2xl streak-flame"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Streaks</dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900">143</div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-1 bg-red-600 rounded-full" style="width: 85%"></div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Longest: 186 days</p>
                    </div>
                </div>
            </div>
            
            <!-- Contest Participants -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-100 rounded-md p-3">
                            <i class="fas fa-trophy text-blue-600 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Contest Participants</dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900">327</div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-1 bg-blue-600 rounded-full" style="width: 65%"></div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">+48% from last month</p>
                    </div>
                </div>
            </div>
            
            <!-- Average Rating -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-red-100 rounded-md p-3">
                            <i class="fas fa-star text-red-600 text-2xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Average Rating</dt>
                                <dd>
                                    <div class="text-lg font-medium text-gray-900">1,584</div>
                                </dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div class="h-1 bg-red-600 rounded-full" style="width: 53%"></div>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Top 25%: 1,892</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaderboard Section -->
    <div id="leaderboard-section" class="bg-gray-50 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:flex lg:items-center lg:justify-between mb-8">
                <div class="flex-1 min-w-0">
                    <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                        College Leaderboard
                    </h2>
                    <p class="mt-1 text-sm text-gray-500">
                        Ranked by total problems solved. Updated every 6 hours.
                    </p>
                </div>
                <div class="mt-5 flex lg:mt-0 lg:ml-4">
                    <div class="mr-4">
                        <select class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option>All Departments</option>
                            <option>Computer Science</option>
                            <option>Data Science</option>
                            <option>Computer Engineering</option>
                            <option>Electrical Engineering</option>
                        </select>
                    </div>
                    <div>
                        <select class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option>All Years</option>
                            <option>Freshman</option>
                            <option>Sophomore</option>
                            <option>Junior</option>
                            <option>Senior</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Rank
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Student
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Department
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Problems Solved
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Contest Rating
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Streak
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                    <span class="sr-only">View</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- Row 1 -->
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-red-100 rounded-full flex items-center justify-center">
                                            <span class="text-red-600 font-bold">1</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">Alex Johnson</div>
                                            <div class="text-sm text-gray-500">Senior</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">Computer Science</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">428</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+12</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">1,892</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+24</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-fire text-red-500 mr-1"></i>
                                        <span class="text-sm text-gray-900 font-bold">186</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                                </td>
                            </tr>
                            
                            <!-- Row 2 -->
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                            <span class="text-blue-600 font-bold">2</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/44.jpg" alt="">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">Sarah Williams</div>
                                            <div class="text-sm text-gray-500">Junior</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">Data Science</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">396</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+8</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">1,847</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+18</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-fire text-red-500 mr-1"></i>
                                        <span class="text-sm text-gray-900 font-bold">92</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                                </td>
                            </tr>
                            
                            <!-- Row 3 -->
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span class="text-gray-600 font-bold">3</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/75.jpg" alt="">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">Michael Chen</div>
                                            <div class="text-sm text-gray-500">Sophomore</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">Computer Engineering</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">372</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+15</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">1,765</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+32</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-fire text-red-500 mr-1"></i>
                                        <span class="text-sm text-gray-900 font-bold">64</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                                </td>
                            </tr>
                            
                            <!-- Row 4 -->
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span class="text-gray-600 font-bold">4</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/women/63.jpg" alt="">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">Emily Rodriguez</div>
                                            <div class="text-sm text-gray-500">Senior</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">Computer Science</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">351</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+5</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">1,723</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+12</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-fire text-red-500 mr-1"></i>
                                        <span class="text-sm text-gray-900 font-bold">45</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                                </td>
                            </tr>
                            
                            <!-- Row 5 -->
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                                            <span class="text-gray-600 font-bold">5</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <img class="h-10 w-10 rounded-full" src="https://randomuser.me/api/portraits/men/81.jpg" alt="">
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">David Kim</div>
                                            <div class="text-sm text-gray-500">Junior</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">Electrical Engineering</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">328</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+9</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 font-bold">1,654</div>
                                    <div class="flex mt-1">
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+15</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <i class="fas fa-fire text-red-500 mr-1"></i>
                                        <span class="text-sm text-gray-900 font-bold">38</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="bg-gray-50 px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Previous
                        </a>
                        <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next
                        </a>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">1</span> to <span class="font-medium">5</span> of <span class="font-medium">127</span> students
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                                <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    1
                                </a>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    2
                                </a>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    3
                                </a>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                                <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    8
                                </a>
                                <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Preview Section -->
    <div id="profile-section" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Your Coding Profile
            </h2>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-4">
                Track your progress and set new goals
            </p>
        </div>
        
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6 bg-blue-800">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <img class="h-16 w-16 rounded-full border-4 border-white" src="https://randomuser.me/api/portraits/men/1.jpg" alt="">
                        <div class="ml-4">
                            <h3 class="text-lg leading-6 font-medium text-white">John Doe</h3>
                            <p class="mt-1 text-sm text-blue-200">Computer Science - Junior</p>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="text-right mr-6">
                            <p class="text-xs text-blue-200">Current Rank</p>
                            <p class="text-2xl font-bold text-white">#27</p>
                        </div>
                        <button class="bg-white text-blue-800 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-100">
                            Edit Profile
                        </button>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
                <dl class="sm:divide-y sm:divide-gray-200">
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">
                            LeetCode Username
                        </dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            johndoe_cs
                            <span class="ml-2 bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">Verified</span>
                        </dd>
                    </div>
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">
                            Problems Solved
                        </dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mr-4">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 62%"></div>
                                </div>
                                <span class="font-bold">217</span>
                            </div>
                            <div class="grid grid-cols-3 gap-4 mt-2">
                                <div>
                                    <p class="text-xs text-gray-500">Easy</p>
                                    <p class="font-medium">134 <span class="text-green-600 text-xs">(+4)</span></p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">Medium</p>
                                    <p class="font-medium">72 <span class="text-green-600 text-xs">(+2)</span></p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">Hard</p>
                                    <p class="font-medium">11 <span class="text-gray-500 text-xs">(+0)</span></p>
                                </div>
                            </div>
                        </dd>
                    </div>
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">
                            Contest Rating
                        </dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <div class="flex items-center">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mr-4">
                                    <div class="bg-red-600 h-2.5 rounded-full" style="width: 45%"></div>
                                </div>
                                <span class="font-bold">1,432</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Global percentile: 68.7%</p>
                        </dd>
                    </div>
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">
                            Current Streak
                        </dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <div class="flex items-center">
                                <i class="fas fa-fire text-red-500 mr-2"></i>
                                <span class="font-bold">28 days</span>
                                <span class="ml-2 bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">Active</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Longest streak: 42 days</p>
                        </dd>
                    </div>
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">
                            Recent Activity
                        </dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <ul class="border border-gray-200 rounded-md divide-y divide-gray-200">
                                <li class="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                                    <div class="w-0 flex-1 flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        <span class="ml-2 flex-1 w-0 truncate">Solved "Two Sum" (Easy)</span>
                                    </div>
                                    <div class="ml-4 flex-shrink-0">
                                        <span class="text-xs text-gray-500">2 hours ago</span>
                                    </div>
                                </li>
                                <li class="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                                    <div class="w-0 flex-1 flex items-center">
                                        <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                                        <span class="ml-2 flex-1 w-0 truncate">Participated in Weekly Contest 298</span>
                                    </div>
                                    <div class="ml-4 flex-shrink-0">
                                        <span class="text-xs text-gray-500">1 day ago</span>
                                    </div>
                                </li>
                                <li class="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                                    <div class="w-0 flex-1 flex items-center">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        <span class="ml-2 flex-1 w-0 truncate">Solved "Longest Substring Without Repeating Characters" (Medium)</span>
                                    </div>
                                    <div class="ml-4 flex-shrink-0">
                                        <span class="text-xs text-gray-500">2 days ago</span>
                                    </div>
                                </li>
                            </ul>
                        </dd>
                    </div>
                    <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">
                            Badges Earned
                        </dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <div class="flex space-x-4">
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-1">
                                        <i class="fas fa-medal text-blue-600 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center">100 Problems</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-1">
                                        <i class="fas fa-fire text-red-600 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center">30 Day Streak</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-1">
                                        <i class="fas fa-star text-yellow-600 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center">Knight Badge</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-1">
                                        <i class="fas fa-plus text-gray-600 text-xl"></i>
                                    </div>
                                    <span class="text-xs text-center">More</span>
                                </div>
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>

    <!-- Analytics Section -->
    <div id="analytics-section" class="bg-gray-50 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                    College Coding Analytics
                </h2>
                <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-4">
                    Insights into our coding community's performance
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                <!-- Problems Solved Over Time -->
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Problems Solved Over Time</h3>
                        <select class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option>Last 6 Months</option>
                            <option>Last Year</option>
                            <option>All Time</option>
                        </select>
                    </div>
                    <div class="h-64 bg-gray-50 rounded-md border border-gray-200 flex items-center justify-center">
                        <!-- Chart placeholder -->
                        <div class="text-center">
                            <i class="fas fa-chart-line text-4xl text-blue-500 mb-2"></i>
                            <p class="text-gray-500">Problems solved visualization</p>
                        </div>
                    </div>
                    <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                        <div>
                            <p class="text-sm text-gray-500">This Month</p>
                            <p class="font-bold text-blue-800">1,284</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Last Month</p>
                            <p class="font-bold text-blue-800">987</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Change</p>
                            <p class="font-bold text-green-600">+30.1%</p>
                        </div>
                    </div>
                </div>
                
                <!-- Problem Difficulty Distribution -->
                <div class="bg-white p-6 rounded-lg shadow">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Problem Difficulty Distribution</h3>
                        <select class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option>All Students</option>
                            <option>Computer Science</option>
                            <option>Data Science</option>
                        </select>
                    </div>
                    <div class="h-64 bg-gray-50 rounded-md border border-gray-200 flex items-center justify-center">
                        <!-- Chart placeholder -->
                        <div class="text-center">
                            <i class="fas fa-chart-pie text-4xl text-red-500 mb-2"></i>
                            <p class="text-gray-500">Difficulty distribution visualization</p>
                        </div>
                    </div>
                    <div class="mt-4 grid grid-cols-3 gap-4 text-center">
                        <div>
                            <p class="text-sm text-gray-500">Easy</p>
                            <p class="font-bold text-green-600">7,542 (58.7%)</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Medium</p>
                            <p class="font-bold text-yellow-600">4,321 (33.6%)</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Hard</p>
                            <p class="font-bold text-red-600">984 (7.7%)</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Department Comparison -->
            <div class="bg-white p-6 rounded-lg shadow mb-12">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Department Comparison</h3>
                    <select class="mt-1 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option>Problems Solved</option>
                        <option>Contest Rating</option>
                        <option>Active Streaks</option>
                    </select>
                </div>
                <div class="h-80 bg-gray-50 rounded-md border border-gray-200 flex items-center justify-center">
                    <!-- Chart placeholder -->
                    <div class="text-center">
                        <i class="fas fa-chart-bar text-4xl text-blue-500 mb-2"></i>
                        <p class="text-gray-500">Department comparison visualization</p>
                    </div>
                </div>
                <div class="mt-4 grid grid-cols-4 gap-4 text-center">
                    <div>
                        <p class="text-sm text-gray-500">Computer Science</p>
                        <p class="font-bold text-blue-800">5,428</p>
                        <p class="text-xs text-gray-500">42.3% of total</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Data Science</p>
                        <p class="font-bold text-blue-800">3,721</p>
                        <p class="text-xs text-gray-500">29.0% of total</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Computer Eng.</p>
                        <p class="font-bold text-blue-800">2,156</p>
                        <p class="text-xs text-gray-500">16.8% of total</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Electrical Eng.</p>
                        <p class="font-bold text-blue-800">1,542</p>
                        <p class="text-xs text-gray-500">12.0% of total</p>
                    </div>
                </div>
            </div>
            
            <!-- Top Problems -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Most Solved Problems</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Problem
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Difficulty
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Solved By
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Acceptance
                                </th>
                                <th scope="col" class="relative px-6 py-3">
                                    <span class="sr-only">View</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Two Sum</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Easy
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">247 students</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">49.2%</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Add Two Numbers</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Medium
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">198 students</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">35.7%</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                                </td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Longest Substring Without Repeating Characters</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Medium
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">176 students</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">31.2%</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="#" class="text-blue-600 hover:text-blue-900">View</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-center">
                    <button class="text-blue-800 font-medium hover:text-blue-600">
                        View All Problems <i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Events Section -->
    <div id="events-section" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                Upcoming Coding Events
            </h2>
            <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 sm:mt-4">
                Join our coding challenges and competitions
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Event 1 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6 bg-blue-800">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-600 rounded-md p-3">
                            <i class="fas fa-trophy text-white text-xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <h3 class="text-lg font-medium text-white">Monthly Coding Challenge</h3>
                            <p class="mt-1 text-sm text-blue-200">Solve 50 problems to win</p>
                        </div>
                    </div>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Starts</dt>
                            <dd class="mt-1 text-sm text-gray-900">June 1, 2023</dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Ends</dt>
                            <dd class="mt-1 text-sm text-gray-900">June 30, 2023</dd>
                        </div>
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Participants</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <div class="flex items-center">
                                    <div class="flex -space-x-1 overflow-hidden">
                                        <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://randomuser.me/api/portraits/men/32.jpg" alt="">
                                        <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://randomuser.me/api/portraits/women/44.jpg" alt="">
                                        <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://randomuser.me/api/portraits/men/75.jpg" alt="">
                                    </div>
                                    <span class="ml-2">87 registered</span>
                                </div>
                            </dd>
                        </div>
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Description</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                Solve 50 problems of any difficulty this month to earn the Coding Champion badge and a chance to win prizes.
                            </dd>
                        </div>
                    </dl>
                </div>
                <div class="px-4 py-4 sm:px-6 bg-gray-50">
                    <div class="flex justify-between">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Register Now
                        </button>
                        <button class="text-blue-600 hover:text-blue-900 px-4 py-2 rounded-md text-sm font-medium">
                            Learn More
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Event 2 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6 bg-red-800">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-red-600 rounded-md p-3">
                            <i class="fas fa-fire text-white text-xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <h3 class="text-lg font-medium text-white">30-Day Streak Challenge</h3>
                            <p class="mt-1 text-sm text-red-200">Maintain your streak for rewards</p>
                        </div>
                    </div>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Starts</dt>
                            <dd class="mt-1 text-sm text-gray-900">June 15, 2023</dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Ends</dt>
                            <dd class="mt-1 text-sm text-gray-900">July 14, 2023</dd>
                        </div>
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Participants</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <div class="flex items-center">
                                    <div class="flex -space-x-1 overflow-hidden">
                                        <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://randomuser.me/api/portraits/women/63.jpg" alt="">
                                        <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://randomuser.me/api/portraits/men/81.jpg" alt="">
                                        <img class="inline-block h-6 w-6 rounded-full ring-2 ring-white" src="https://randomuser.me/api/portraits/women/22.jpg" alt="">
                                    </div>
                                    <span class="ml-2">64 registered</span>
                                </div>
                            </dd>
                        </div>
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Description</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                Solve at least one problem every day for 30 days straight to earn the Fire Keeper badge and special recognition.
                            </dd>
                        </div>
                    </dl>
                </div>
                <div class="px-4 py-4 sm:px-6 bg-gray-50">
                    <div class="flex justify-between">
                        <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Register Now
                        </button>
                        <button class="text-red-600 hover:text-red-900 px-4 py-2 rounded-md text-sm font-medium">
                            Learn More
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Event 3 -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6 bg-blue-800">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 bg-blue-600 rounded-md p-3">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <h3 class="text-lg font-medium text-white">Department Showdown</h3>
                            <p class="mt-1 text-sm text-blue-200">Which department will reign supreme?</p>
                        </div>
                    </div>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-2">
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Starts</dt>
                            <dd class="mt-1 text-sm text-gray-900">July 1, 2023</dd>
                        </div>
                        <div class="sm:col-span-1">
                            <dt class="text-sm font-medium text-gray-500">Ends</dt>
                            <dd class="mt-1 text-sm text-gray-900">July 31, 2023</dd>
                        </div>
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Departments</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded">CS</span>
                                    <span class="bg-red-100 text-red-800 text-xs px-2 py-0.5 rounded">DS</span>
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">CE</span>
                                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded">EE</span>
                                </div>
                            </dd>
                        </div>
                        <div class="sm:col-span-2">
                            <dt class="text-sm font-medium text-gray-500">Description</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                Compete to solve the most problems as a department. The winning department gets bragging rights and a pizza party!
                            </dd>
                        </div>
                    </dl>
                </div>
                <div class="px-4 py-4 sm:px-6 bg-gray-50">
                    <div class="flex justify-between">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Register Now
                        </button>
                        <button class="text-blue-600 hover:text-blue-900 px-4 py-2 rounded-md text-sm font-medium">
                            Learn More
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <div id="cta-section" class="gradient-bg">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
                    Ready to join the competition?
                </h2>
                <p class="mt-3 max-w-2xl mx-auto text-xl text-blue-100 sm:mt-4">
                    Connect your LeetCode profile and start tracking your progress today.
                </p>
                <div class="mt-8 flex justify-center">
                    <div class="inline-flex rounded-md shadow">
                        <a href="#" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-blue-800 bg-white hover:bg-gray-50">
                            Get Started
                        </a>
                    </div>
                    <div class="ml-3 inline-flex">
                        <a href="#" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-800 bg-opacity-60 hover:bg-opacity-70">
                            Learn more
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white">
        <div class="max-w-7xl mx-auto py-12 px-4 overflow-hidden sm:px-6 lg:px-8">
            <nav class="-mx-5 -my-2 flex flex-wrap justify-center" aria-label="Footer">
                <div class="px-5 py-2">
                    <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                        About
                    </a>
                </div>
                <div class="px-5 py-2">
                    <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                        Leaderboard
                    </a>
                </div>
                <div class="px-5 py-2">
                    <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                        Analytics
                    </a>
                </div>
                <div class="px-5 py-2">
                    <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                        Events
                    </a>
                </div>
                <div class="px-5 py-2">
                    <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                        Privacy
                    </a>
                </div>
                <div class="px-5 py-2">
                    <a href="#" class="text-base text-gray-500 hover:text-gray-900">
                        Terms
                    </a>
                </div>
            </nav>
            <div class="mt-8 flex justify-center space-x-6">
                <a href="#" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">Facebook</span>
                    <i class="fab fa-facebook text-xl"></i>
                </a>
                <a href="#" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">Twitter</span>
                    <i class="fab fa-twitter text-xl"></i>
                </a>
                <a href="#" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">GitHub</span>
                    <i class="fab fa-github text-xl"></i>
                </a>
                <a href="#" class="text-gray-400 hover:text-gray-500">
                    <span class="sr-only">LinkedIn</span>
                    <i class="fab fa-linkedin text-xl"></i>
                </a>
            </div>
            <p class="mt-8 text-center text-base text-gray-400">
                &copy; 2023 CodeRank. All rights reserved.
            </p>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.querySelector('[aria-controls="mobile-menu"]').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // Mobile menu toggle
        document.querySelector('[aria-controls="mobile-menu"]').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // Simulate badge earning animation
        function earnBadge() {
            const badge = document.createElement('div');
            badge.className = 'fixed inset-0 flex items-center justify-center z-50';
            badge.innerHTML = `
                <div class="bg-white p-8 rounded-xl shadow-2xl max-w-sm text-center transform transition-all duration-500 scale-0 badge-pop">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
                        <i class="fas fa-trophy text-yellow-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Badge Earned!</h3>
                    <p class="text-sm text-gray-500">You've earned the "Problem Solver" badge!</p>
                    <button onclick="this.parentElement.parentElement.remove()" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                        Awesome!
                    </button>
                </div>
            `;
            document.body.appendChild(badge);

            setTimeout(() => {
                badge.querySelector('div').classList.add('scale-100');
                badge.querySelector('div').classList.remove('scale-0');
            }, 100);
        }

        // Confetti animation
        function createConfetti() {
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.backgroundColor = ['#f00', '#0f0', '#00f', '#ff0', '#f0f', '#0ff'][Math.floor(Math.random() * 6)];
                confetti.style.animationDelay = Math.random() * 3 + 's';
                confetti.style.animationDuration = Math.random() * 3 + 2 + 's';
                document.body.appendChild(confetti);

                setTimeout(() => confetti.remove(), 5000);
            }
        }

        // Add confetti animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confetti-fall {
                0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
                100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
            }
            .confetti {
                animation: confetti-fall linear infinite;
            }
        `;
        document.head.appendChild(style);
    </script>
    <script src="app.js"></script>
</body>
</html>