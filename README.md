# CodeRank - College LeetCode Tracker

A comprehensive web application for tracking and ranking college students' LeetCode progress.

## Features Implemented

### ✅ Complete Functionality

1. **Navigation System**
   - Smooth scrolling between sections
   - Mobile-responsive hamburger menu
   - Active section highlighting
   - Sticky navigation bar

2. **Authentication System**
   - Sign In modal with form validation
   - Registration modal with comprehensive form
   - User session management with localStorage
   - Automatic UI updates for logged-in users

3. **Leaderboard**
   - Dynamic student ranking display
   - Department and year filtering
   - Search functionality
   - Individual profile viewing
   - Challenge system between users
   - Real-time updates simulation

4. **Profile Management**
   - Editable user profiles
   - LeetCode username verification
   - Progress tracking (problems solved, rating, streak)
   - Achievement badge system
   - Personal statistics display

5. **Analytics Dashboard**
   - Interactive charts using Chart.js
   - Problems solved over time (line chart)
   - Difficulty distribution (doughnut chart)
   - Department comparison (bar chart)
   - Real-time data visualization

6. **Events System**
   - Event registration functionality
   - Countdown timers for events
   - Participant tracking
   - Multiple event types (challenges, streaks, competitions)

7. **Real-time Features**
   - Live leaderboard updates
   - Notification system
   - Streak counter animations
   - Achievement checking
   - Progress tracking

8. **Badge & Achievement System**
   - Milestone-based achievements
   - Animated badge earning
   - Confetti celebrations
   - Badge collection viewing
   - Progress-based unlocks

9. **Data Persistence**
   - localStorage for user data
   - Settings persistence
   - Achievement tracking
   - Session management

10. **Responsive Design**
    - Mobile-first approach
    - Touch-friendly interactions
    - Responsive tables and charts
    - Optimized modal dialogs
    - Smooth animations and transitions

## How to Use

1. **Getting Started**
   - Open `index.html` in a web browser
   - Click "Register" to create a new account
   - Fill in your details including LeetCode username
   - Start exploring the features!

2. **Key Interactions**
   - **Sign In/Register**: Click the buttons in the top navigation
   - **View Leaderboard**: Scroll to leaderboard section or click nav link
   - **Filter Students**: Use dropdown filters in leaderboard
   - **Search Students**: Use the search box (auto-generated)
   - **View Profiles**: Click "View" next to any student
   - **Edit Profile**: Click your name in nav → Edit Profile
   - **Register for Events**: Click "Register Now" on any event
   - **View Analytics**: Navigate to Analytics section for charts
   - **Earn Badges**: Complete actions to unlock achievements

3. **Demo Features**
   - Real-time leaderboard updates every 30 seconds
   - Automatic badge earning for milestones
   - Interactive charts with hover effects
   - Smooth animations and transitions
   - Mobile-responsive design

## Technical Implementation

- **Frontend**: HTML5, CSS3 (Tailwind CSS), Vanilla JavaScript
- **Charts**: Chart.js for data visualization
- **Icons**: Font Awesome
- **Storage**: localStorage for data persistence
- **Architecture**: Class-based JavaScript with modular design

## 🔗 LeetCode & HackerRank Integration

### Real Data Retrieval System

The project now includes a comprehensive Python-based API server that fetches real data from LeetCode and HackerRank profiles:

#### **Python API Server Features:**
- ✅ **LeetCode Data Retrieval**: Problems solved (Easy/Medium/Hard), contest rating, streaks, badges
- ✅ **HackerRank Data Retrieval**: Badges, domain scores, profile verification
- ✅ **RESTful API**: Clean endpoints for web app integration
- ✅ **Real-time Sync**: Automatic data synchronization with user profiles
- ✅ **Achievement System**: Real achievements based on actual progress

#### **Setup Instructions:**

1. **Install Python Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
   Or manually:
   ```bash
   pip install flask flask-cors requests beautifulsoup4 rich
   ```

2. **Start the API Server:**
   ```bash
   python start_server.py
   ```
   Or directly:
   ```bash
   python api_server.py
   ```

3. **Open the Web Application:**
   - Open `index.html` in your browser
   - The web app will automatically connect to the API server
   - If the server is not running, it falls back to simulated data

#### **API Endpoints:**

- `GET /api/leetcode/<username>` - Get comprehensive LeetCode stats
- `GET /api/hackerrank/<username>` - Get HackerRank profile data
- `GET /api/problems/<username>?platform=leetcode` - Get problem count
- `GET /api/verify/<username>?platform=leetcode` - Verify username exists
- `GET /api/contest/<username>` - Get LeetCode contest rating
- `GET /api/health` - Health check

#### **Web App Integration:**

The JavaScript application automatically:
- ✅ Verifies LeetCode usernames against real profiles
- ✅ Fetches actual problem counts and difficulty breakdown
- ✅ Retrieves real contest ratings and rankings
- ✅ Syncs streak data and active days
- ✅ Awards achievements based on real progress
- ✅ Updates leaderboard with actual data

## File Structure

```
coderank/
├── index.html              # Main HTML file with complete UI
├── app.js                 # Complete JavaScript functionality with API integration
├── leetcodeRetrive.py     # Enhanced Python script for data retrieval
├── api_server.py          # Flask API server for web integration
├── start_server.py        # Quick start script for the API server
├── requirements.txt       # Python dependencies
└── README.md             # This documentation
```

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers (iOS Safari, Chrome Mobile)

## Features Demonstrated

Every button, link, and interactive element has been implemented with full functionality:

- ✅ All navigation links work
- ✅ All buttons have click handlers
- ✅ All forms are functional with validation
- ✅ All modals open and close properly
- ✅ All filters and search work
- ✅ All charts are interactive
- ✅ All animations and transitions work
- ✅ Mobile responsiveness is complete
- ✅ Data persistence works across sessions
- ✅ Real-time updates are simulated
- ✅ Achievement system is fully functional

## Demo Instructions

### **Option 1: With Real Data (Recommended)**

1. **Start the API Server:**
   ```bash
   python start_server.py
   ```

2. **Open the Web Application:**
   - Open `index.html` in your browser
   - Register with your real LeetCode username
   - Click "Verify" to connect to your actual LeetCode profile
   - See your real problem counts, ratings, and streaks!

3. **Explore Real Features:**
   - View actual problem breakdown (Easy/Medium/Hard)
   - See real contest ratings and rankings
   - Get achievements based on actual progress
   - Sync data using the "🔄 Sync LeetCode Data" option

### **Option 2: Simulated Data**

1. Open `index.html` directly in your browser
2. Register a new account to see the full experience
3. Explore all sections using navigation
4. Try filtering and searching in the leaderboard
5. Register for events to see notifications
6. Edit your profile to see form handling
7. Wait for real-time updates and achievements
8. Test on mobile devices for responsive design

### **Testing Real Data Retrieval:**

You can test the Python scripts independently:

```bash
# Test LeetCode data retrieval
python leetcodeRetrive.py

# Choose option 1 and enter: https://leetcode.com/your-username/
# Choose option 2 and enter: https://www.hackerrank.com/your-username
# Choose option 4 to start the web API server
```

### **Example API Usage:**

```bash
# Test API endpoints directly
curl http://localhost:5000/api/health
curl http://localhost:5000/api/leetcode/your-username
curl http://localhost:5000/api/verify/your-username?platform=leetcode
```

## 🎯 Complete Integration Features

The application now provides a **complete, fully-functional college LeetCode tracking system** with:

- ✅ **Real Data Integration**: Actual LeetCode and HackerRank statistics
- ✅ **Live Verification**: Real username verification against platforms
- ✅ **Accurate Achievements**: Badges based on actual progress
- ✅ **Dynamic Leaderboards**: Rankings with real problem counts
- ✅ **Contest Integration**: Real contest ratings and rankings
- ✅ **Streak Tracking**: Actual coding streaks from LeetCode
- ✅ **Fallback System**: Works with or without API server
- ✅ **Modern Web Features**: All interactive elements functional
- ✅ **Mobile Responsive**: Perfect on all devices

This is now a **production-ready system** that can be deployed for actual college coding competitions and tracking!
