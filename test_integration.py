#!/usr/bin/env python3
"""
Test script to demonstrate LeetCode and HackerRank data retrieval
"""

from leetcodeRetrive import (
    get_leetcode_stats, 
    get_hackerrank_stats, 
    get_total_problems_solved,
    get_contest_rating,
    get_comprehensive_stats
)
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
import json

console = Console()

def test_leetcode_integration():
    """Test LeetCode data retrieval with sample usernames"""
    console.print(Panel.fit("🟢 Testing LeetCode Integration", style="bold green"))
    
    # Test with some popular LeetCode usernames (these are public profiles)
    test_usernames = [
        "lee215",  # Popular competitive programmer
        "awice",   # LeetCode staff
        "votrubac" # Active user
    ]
    
    for username in test_usernames:
        console.print(f"\n📊 Testing username: [bold cyan]{username}[/bold cyan]")
        
        try:
            # Test comprehensive stats
            stats = get_comprehensive_stats(username, 'leetcode')
            
            if 'error' not in stats:
                console.print(f"✅ Successfully retrieved data for {username}")
                
                # Create summary table
                table = Table(title=f"LeetCode Stats - {username}")
                table.add_column("Metric", style="cyan")
                table.add_column("Value", style="green")
                
                table.add_row("Total Problems", str(stats.get('problems_solved', {}).get('total', 'N/A')))
                table.add_row("Easy Problems", str(stats.get('problems_solved', {}).get('easy', 'N/A')))
                table.add_row("Medium Problems", str(stats.get('problems_solved', {}).get('medium', 'N/A')))
                table.add_row("Hard Problems", str(stats.get('problems_solved', {}).get('hard', 'N/A')))
                table.add_row("Contest Rating", str(stats.get('contest_rating', 'N/A')))
                table.add_row("Current Streak", str(stats.get('current_streak', 'N/A')))
                table.add_row("Highest Streak", str(stats.get('highest_streak', 'N/A')))
                table.add_row("Badges Count", str(len(stats.get('badges', []))))
                
                console.print(table)
                
                # Test individual functions
                problems = get_total_problems_solved(username, 'leetcode')
                if 'error' not in problems:
                    console.print(f"📈 Total problems function: {problems['total']} problems")
                
                contest = get_contest_rating(username, 'leetcode')
                if 'error' not in contest:
                    console.print(f"🏆 Contest rating function: {contest.get('rating', 'N/A')}")
                
            else:
                console.print(f"❌ Error for {username}: {stats['error']}")
                
        except Exception as e:
            console.print(f"❌ Exception for {username}: {str(e)}")
        
        console.print("-" * 50)

def test_api_endpoints():
    """Test API endpoints if server is running"""
    console.print(Panel.fit("🌐 Testing API Endpoints", style="bold blue"))
    
    try:
        import requests
        
        base_url = "http://localhost:5000"
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/api/health", timeout=5)
            if response.status_code == 200:
                console.print("✅ API server is running!")
                
                # Test LeetCode endpoint
                test_username = "lee215"
                response = requests.get(f"{base_url}/api/leetcode/{test_username}", timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        console.print(f"✅ LeetCode API endpoint working for {test_username}")
                        console.print(f"📊 Problems solved: {data['data'].get('problems_solved', {}).get('total', 'N/A')}")
                    else:
                        console.print(f"❌ API returned error: {data.get('error', 'Unknown error')}")
                else:
                    console.print(f"❌ API request failed: {response.status_code}")
                
                # Test verification endpoint
                response = requests.get(f"{base_url}/api/verify/{test_username}?platform=leetcode", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success') and data.get('valid'):
                        console.print(f"✅ Verification endpoint working - {test_username} is valid")
                    else:
                        console.print(f"❌ Verification failed: {data.get('error', 'Unknown error')}")
                
            else:
                console.print(f"❌ API server responded with status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            console.print("❌ API server is not running")
            console.print("💡 Start the server with: python start_server.py")
        except requests.exceptions.Timeout:
            console.print("❌ API server timeout")
        
    except ImportError:
        console.print("❌ Requests library not installed")
        console.print("💡 Install with: pip install requests")

def test_web_integration():
    """Test web integration features"""
    console.print(Panel.fit("🌐 Web Integration Test", style="bold magenta"))
    
    console.print("📝 Testing features that the web app uses:")
    
    # Test username verification (what the web app does)
    test_username = "lee215"
    
    console.print(f"\n1. Username Verification for '{test_username}':")
    profile_url = f"https://leetcode.com/{test_username}/"
    stats = get_leetcode_stats(profile_url)
    
    if 'error' not in stats:
        console.print("   ✅ Username exists and is accessible")
        console.print(f"   📊 Can retrieve {stats['solved_problems']['total']} problems solved")
    else:
        console.print(f"   ❌ Verification failed: {stats['error']}")
    
    console.print("\n2. Problem Count Retrieval:")
    problems = get_total_problems_solved(test_username, 'leetcode')
    if 'error' not in problems:
        console.print(f"   ✅ Easy: {problems['easy']}, Medium: {problems['medium']}, Hard: {problems['hard']}")
        console.print(f"   📈 Total: {problems['total']} problems")
    else:
        console.print(f"   ❌ Failed: {problems['error']}")
    
    console.print("\n3. Contest Rating:")
    contest = get_contest_rating(test_username, 'leetcode')
    if 'error' not in contest:
        console.print(f"   🏆 Rating: {contest.get('rating', 'N/A')}")
        console.print(f"   🌍 Global Rank: {contest.get('global_ranking', 'N/A')}")
    else:
        console.print(f"   ❌ Failed: {contest['error']}")

def main():
    """Main test function"""
    console.print(Panel.fit("🧪 CodeRank Integration Test Suite", style="bold yellow"))
    
    console.print("\n[yellow]This script tests the LeetCode/HackerRank integration features[/yellow]")
    console.print("[yellow]that power the CodeRank web application.[/yellow]\n")
    
    # Run tests
    test_leetcode_integration()
    console.print("\n")
    test_api_endpoints()
    console.print("\n")
    test_web_integration()
    
    console.print("\n" + "="*60)
    console.print(Panel.fit("🎉 Test Suite Complete!", style="bold green"))
    
    console.print("\n[cyan]Next steps:[/cyan]")
    console.print("1. 🚀 Start API server: [bold]python start_server.py[/bold]")
    console.print("2. 🌐 Open web app: [bold]index.html[/bold]")
    console.print("3. 📝 Register with your LeetCode username")
    console.print("4. ✅ Verify your profile to see real data!")

if __name__ == "__main__":
    main()
