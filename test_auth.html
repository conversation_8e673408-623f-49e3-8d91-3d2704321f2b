<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeRank Auth Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 class="text-2xl font-bold text-center mb-6 text-gray-800">
            <i class="fas fa-code text-red-600 mr-2"></i>
            CodeRank Auth Test
        </h1>
        
        <div class="space-y-4">
            <button id="test-signin" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-md font-medium transition duration-200">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Test Sign In Modal
            </button>
            
            <button id="test-register" class="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-md font-medium transition duration-200">
                <i class="fas fa-user-plus mr-2"></i>
                Test Register Modal
            </button>
            
            <button id="test-verify" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-md font-medium transition duration-200">
                <i class="fas fa-check-circle mr-2"></i>
                Test LeetCode Verification
            </button>
            
            <div class="border-t pt-4">
                <h3 class="font-medium text-gray-700 mb-2">Test Status:</h3>
                <div id="status" class="text-sm text-gray-600">
                    Click buttons above to test authentication features
                </div>
            </div>
        </div>
        
        <div class="mt-6 text-center">
            <a href="index.html" class="text-blue-600 hover:text-blue-800 text-sm">
                ← Back to Main App
            </a>
        </div>
    </div>

    <script src="app.js"></script>
    <script>
        // Wait for app to be initialized
        function waitForApp() {
            if (typeof app !== 'undefined' && app) {
                console.log('✅ App is ready');
                setupTestButtons();
            } else {
                console.log('⏳ Waiting for app...');
                setTimeout(waitForApp, 100);
            }
        }

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            const colors = {
                success: 'text-green-600',
                error: 'text-red-600',
                info: 'text-blue-600'
            };
            statusEl.className = `text-sm ${colors[type] || colors.info}`;
            statusEl.textContent = message;
        }

        function setupTestButtons() {
            updateStatus('✅ App loaded successfully! Ready to test.', 'success');

            // Test Sign In Modal
            document.getElementById('test-signin').addEventListener('click', function() {
                try {
                    if (app && app.showSignInModal) {
                        app.showSignInModal();
                        updateStatus('✅ Sign In modal opened successfully', 'success');
                    } else {
                        updateStatus('❌ Sign In modal function not found', 'error');
                    }
                } catch (error) {
                    updateStatus(`❌ Error: ${error.message}`, 'error');
                }
            });

            // Test Register Modal
            document.getElementById('test-register').addEventListener('click', function() {
                try {
                    if (app && app.showRegisterModal) {
                        app.showRegisterModal();
                        updateStatus('✅ Register modal opened successfully', 'success');
                    } else {
                        updateStatus('❌ Register modal function not found', 'error');
                    }
                } catch (error) {
                    updateStatus(`❌ Error: ${error.message}`, 'error');
                }
            });

            // Test LeetCode Verification
            document.getElementById('test-verify').addEventListener('click', function() {
                try {
                    if (app && app.verifyLeetCodeUsername) {
                        // Create a test user first
                        app.currentUser = {
                            leetcodeUsername: 'lee215' // Test with a known username
                        };
                        app.verifyLeetCodeUsername();
                        updateStatus('✅ LeetCode verification started', 'success');
                    } else {
                        updateStatus('❌ LeetCode verification function not found', 'error');
                    }
                } catch (error) {
                    updateStatus(`❌ Error: ${error.message}`, 'error');
                }
            });
        }

        // Start waiting for app
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('⏳ Loading CodeRank app...', 'info');
            waitForApp();
        });
    </script>
</body>
</html>
