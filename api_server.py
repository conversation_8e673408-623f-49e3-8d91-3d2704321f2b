#!/usr/bin/env python3
"""
CodeRank API Server
A simple Flask server to provide LeetCode and HackerRank data to the web application
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import sys
import os

# Add the current directory to Python path to import leetcodeRetrive
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from leetcodeRetrive import (
        get_comprehensive_stats, 
        get_total_problems_solved, 
        get_contest_rating,
        get_leetcode_stats,
        get_hackerrank_stats
    )
except ImportError as e:
    print(f"Error importing leetcodeRetrive: {e}")
    print("Make sure leetcodeRetrive.py is in the same directory")
    sys.exit(1)

app = Flask(__name__)
CORS(app)  # Enable CORS for web app integration

@app.route('/')
def home():
    """Home endpoint with API documentation"""
    return jsonify({
        'service': 'CodeRank Data API',
        'version': '1.0.0',
        'endpoints': {
            'GET /api/leetcode/<username>': 'Get comprehensive LeetCode stats',
            'GET /api/hackerrank/<username>': 'Get comprehensive HackerRank stats',
            'GET /api/problems/<username>': 'Get total problems solved (add ?platform=leetcode/hackerrank)',
            'GET /api/verify/<username>': 'Verify username exists (add ?platform=leetcode/hackerrank)',
            'GET /api/contest/<username>': 'Get LeetCode contest rating',
            'GET /api/health': 'Health check'
        }
    })

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'CodeRank Data API',
        'message': 'Server is running properly'
    })

@app.route('/api/leetcode/<username>')
def get_leetcode_user_stats(username):
    """API endpoint to get comprehensive LeetCode stats for a user"""
    try:
        stats = get_comprehensive_stats(username, 'leetcode')
        
        if 'error' in stats:
            return jsonify({'error': stats['error']}), 404
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500

@app.route('/api/hackerrank/<username>')
def get_hackerrank_user_stats(username):
    """API endpoint to get comprehensive HackerRank stats for a user"""
    try:
        stats = get_comprehensive_stats(username, 'hackerrank')
        
        if 'error' in stats:
            return jsonify({'error': stats['error']}), 404
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500

@app.route('/api/problems/<username>')
def get_user_problems(username):
    """API endpoint to get total problems solved"""
    platform = request.args.get('platform', 'leetcode').lower()
    
    try:
        problems = get_total_problems_solved(username, platform)
        
        if 'error' in problems:
            return jsonify({'error': problems['error']}), 404
        
        return jsonify({
            'success': True,
            'data': problems
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500

@app.route('/api/verify/<username>')
def verify_username(username):
    """API endpoint to verify if a username exists on the platform"""
    platform = request.args.get('platform', 'leetcode').lower()
    
    try:
        if platform == 'leetcode':
            profile_url = f"https://leetcode.com/{username}/"
            stats = get_leetcode_stats(profile_url)
        elif platform == 'hackerrank':
            profile_url = f"https://www.hackerrank.com/{username}"
            stats = get_hackerrank_stats(profile_url)
        else:
            return jsonify({
                'success': False,
                'error': 'Unsupported platform. Use "leetcode" or "hackerrank"'
            }), 400
        
        if 'error' in stats:
            return jsonify({
                'success': False,
                'valid': False,
                'error': stats['error']
            }), 404
        else:
            return jsonify({
                'success': True,
                'valid': True,
                'username': username,
                'platform': platform.title(),
                'message': f'Username {username} exists on {platform.title()}'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'valid': False,
            'error': f'Server error: {str(e)}'
        }), 500

@app.route('/api/contest/<username>')
def get_user_contest_rating(username):
    """API endpoint to get LeetCode contest rating"""
    try:
        rating_data = get_contest_rating(username, 'leetcode')
        
        if 'error' in rating_data:
            return jsonify({'error': rating_data['error']}), 404
        
        return jsonify({
            'success': True,
            'data': rating_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found',
        'message': 'Check the API documentation at the root endpoint'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    return jsonify({
        'success': False,
        'error': 'Internal server error',
        'message': 'Something went wrong on the server'
    }), 500

def main():
    """Main function to run the API server"""
    print("🚀 Starting CodeRank API Server...")
    print("📊 Available endpoints:")
    print("  • GET / - API documentation")
    print("  • GET /api/health - Health check")
    print("  • GET /api/leetcode/<username> - LeetCode stats")
    print("  • GET /api/hackerrank/<username> - HackerRank stats")
    print("  • GET /api/problems/<username>?platform=leetcode - Problem count")
    print("  • GET /api/verify/<username>?platform=leetcode - Verify username")
    print("  • GET /api/contest/<username> - Contest rating")
    print("\n🌐 Server will be available at: http://localhost:5000")
    print("🔗 CORS enabled for web app integration")
    print("\n⚠️  Make sure you have installed: pip install flask flask-cors requests beautifulsoup4 rich")
    
    try:
        app.run(
            host='0.0.0.0',  # Allow external connections
            port=5000,
            debug=True,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")

if __name__ == "__main__":
    main()
