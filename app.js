// CodeRank Application JavaScript
class CodeRankApp {
    constructor() {
        this.currentUser = null;
        this.leaderboardData = [];
        this.events = [];
        this.init();
    }

    init() {
        this.loadUserData();
        this.setupEventListeners();
        this.setupNavigation();
        this.loadLeaderboardData();
        this.loadEventsData();
        this.setupCharts();
        this.startRealTimeUpdates();
    }

    // Navigation functionality
    setupNavigation() {
        // Mobile menu toggle
        const mobileMenuButton = document.querySelector('[aria-controls="mobile-menu"]');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('nav a[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                
                // Close mobile menu if open
                if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
            });
        });

        // Active navigation highlighting
        window.addEventListener('scroll', () => {
            this.updateActiveNavigation();
        });
    }

    updateActiveNavigation() {
        const sections = ['home-section', 'stats-section', 'leaderboard-section', 'profile-section', 'analytics-section', 'events-section'];
        const navLinks = document.querySelectorAll('nav a');
        
        let currentSection = '';
        
        sections.forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) {
                const rect = section.getBoundingClientRect();
                if (rect.top <= 100 && rect.bottom >= 100) {
                    currentSection = sectionId;
                }
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('border-blue-500', 'text-blue-800');
            link.classList.add('border-transparent', 'text-gray-600');
        });

        if (currentSection) {
            const activeLink = document.querySelector(`nav a[href="#${currentSection}"]`);
            if (activeLink) {
                activeLink.classList.remove('border-transparent', 'text-gray-600');
                activeLink.classList.add('border-blue-500', 'text-blue-800');
            }
        }
    }

    // Event listeners setup
    setupEventListeners() {
        // Sign In button
        document.querySelectorAll('button').forEach(button => {
            if (button.textContent.includes('Sign In')) {
                button.addEventListener('click', () => this.showSignInModal());
            }
            if (button.textContent.includes('Register')) {
                button.addEventListener('click', () => this.showRegisterModal());
            }
            if (button.textContent.includes('Get Started')) {
                button.addEventListener('click', () => this.showRegisterModal());
            }
        });

        // Leaderboard filters
        const departmentFilter = document.querySelector('select');
        if (departmentFilter) {
            departmentFilter.addEventListener('change', () => this.filterLeaderboard());
        }

        // Profile edit button
        const editProfileBtn = document.querySelector('button:contains("Edit Profile")');
        if (editProfileBtn) {
            editProfileBtn.addEventListener('click', () => this.showEditProfileModal());
        }

        // Event registration buttons
        document.querySelectorAll('button').forEach(button => {
            if (button.textContent.includes('Register Now')) {
                button.addEventListener('click', (e) => {
                    const eventCard = e.target.closest('.bg-white');
                    const eventTitle = eventCard.querySelector('h3').textContent;
                    this.registerForEvent(eventTitle);
                });
            }
        });
    }

    // User data management
    loadUserData() {
        const userData = localStorage.getItem('coderank_user');
        if (userData) {
            this.currentUser = JSON.parse(userData);
            this.updateUIForLoggedInUser();
        }
    }

    saveUserData() {
        if (this.currentUser) {
            localStorage.setItem('coderank_user', JSON.stringify(this.currentUser));
        }
    }

    updateUIForLoggedInUser() {
        if (this.currentUser) {
            // Update sign in buttons to show user menu
            document.querySelectorAll('button').forEach(button => {
                if (button.textContent.includes('Sign In')) {
                    button.textContent = this.currentUser.name;
                    button.onclick = () => this.showUserMenu();
                }
                if (button.textContent.includes('Register')) {
                    button.style.display = 'none';
                }
            });

            // Update profile section with user data
            this.updateProfileSection();
        }
    }

    updateProfileSection() {
        const profileSection = document.getElementById('profile-section');
        if (profileSection && this.currentUser) {
            const nameElement = profileSection.querySelector('h3');
            const departmentElement = profileSection.querySelector('p');
            const rankElement = profileSection.querySelector('.text-2xl');
            
            if (nameElement) nameElement.textContent = this.currentUser.name;
            if (departmentElement) departmentElement.textContent = `${this.currentUser.department} - ${this.currentUser.year}`;
            if (rankElement) rankElement.textContent = `#${this.currentUser.rank || 'N/A'}`;
        }
    }

    // Authentication modals
    showSignInModal() {
        const modal = this.createModal('Sign In', `
            <form id="signin-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-600">Remember me</span>
                    </label>
                    <a href="#" class="text-sm text-blue-600 hover:text-blue-500">Forgot password?</a>
                </div>
                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Sign In
                </button>
            </form>
        `);

        document.getElementById('signin-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSignIn(e.target);
        });
    }

    showRegisterModal() {
        const modal = this.createModal('Register', `
            <form id="register-form" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">First Name</label>
                        <input type="text" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Last Name</label>
                        <input type="text" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Department</label>
                    <select required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Department</option>
                        <option value="Computer Science">Computer Science</option>
                        <option value="Data Science">Data Science</option>
                        <option value="Computer Engineering">Computer Engineering</option>
                        <option value="Electrical Engineering">Electrical Engineering</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Year</label>
                    <select required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Year</option>
                        <option value="Freshman">Freshman</option>
                        <option value="Sophomore">Sophomore</option>
                        <option value="Junior">Junior</option>
                        <option value="Senior">Senior</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">LeetCode Username</label>
                    <input type="text" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input type="password" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <button type="submit" class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                    Register
                </button>
            </form>
        `);

        document.getElementById('register-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister(e.target);
        });
    }

    createModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                    <button class="text-gray-400 hover:text-gray-600" onclick="this.closest('.fixed').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                ${content}
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        return modal;
    }

    handleSignIn(form) {
        const formData = new FormData(form);
        const email = formData.get('email') || form.querySelector('input[type="email"]').value;
        const password = formData.get('password') || form.querySelector('input[type="password"]').value;
        
        // Simulate authentication
        this.currentUser = {
            id: Date.now(),
            name: 'John Doe',
            email: email,
            department: 'Computer Science',
            year: 'Junior',
            leetcodeUsername: 'johndoe_cs',
            problemsSolved: 217,
            contestRating: 1432,
            streak: 28,
            rank: 27
        };
        
        this.saveUserData();
        this.updateUIForLoggedInUser();
        form.closest('.fixed').remove();
        this.showNotification('Successfully signed in!', 'success');
    }

    handleRegister(form) {
        const inputs = form.querySelectorAll('input, select');
        const data = {};
        
        inputs.forEach(input => {
            if (input.type !== 'submit') {
                data[input.previousElementSibling.textContent.toLowerCase().replace(' ', '')] = input.value;
            }
        });
        
        // Validate passwords match
        const password = form.querySelector('input[type="password"]').value;
        const confirmPassword = form.querySelectorAll('input[type="password"]')[1].value;
        
        if (password !== confirmPassword) {
            this.showNotification('Passwords do not match!', 'error');
            return;
        }
        
        // Create user
        this.currentUser = {
            id: Date.now(),
            name: `${data.firstname} ${data.lastname}`,
            email: data.email,
            department: data.department,
            year: data.year,
            leetcodeUsername: data.leetcodeusername,
            problemsSolved: 0,
            contestRating: 0,
            streak: 0,
            rank: null
        };
        
        this.saveUserData();
        this.updateUIForLoggedInUser();
        form.closest('.fixed').remove();
        this.showNotification('Registration successful! Welcome to CodeRank!', 'success');
        this.earnBadge('Welcome Badge', 'Welcome to CodeRank!');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

        const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        notification.classList.add(bgColor, 'text-white');

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation' : 'info'}-circle mr-2"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }

    // Leaderboard functionality
    loadLeaderboardData() {
        // Sample leaderboard data
        this.leaderboardData = [
            { id: 1, name: 'Alex Johnson', department: 'Computer Science', year: 'Senior', problems: 428, rating: 1892, streak: 186, avatar: 'https://randomuser.me/api/portraits/men/32.jpg' },
            { id: 2, name: 'Sarah Williams', department: 'Data Science', year: 'Junior', problems: 396, rating: 1847, streak: 92, avatar: 'https://randomuser.me/api/portraits/women/44.jpg' },
            { id: 3, name: 'Michael Chen', department: 'Computer Engineering', year: 'Sophomore', problems: 372, rating: 1765, streak: 64, avatar: 'https://randomuser.me/api/portraits/men/75.jpg' },
            { id: 4, name: 'Emily Rodriguez', department: 'Computer Science', year: 'Senior', problems: 351, rating: 1723, streak: 45, avatar: 'https://randomuser.me/api/portraits/women/63.jpg' },
            { id: 5, name: 'David Kim', department: 'Electrical Engineering', year: 'Junior', problems: 328, rating: 1654, streak: 38, avatar: 'https://randomuser.me/api/portraits/men/81.jpg' },
            { id: 6, name: 'Jessica Park', department: 'Data Science', year: 'Senior', problems: 315, rating: 1632, streak: 52, avatar: 'https://randomuser.me/api/portraits/women/22.jpg' },
            { id: 7, name: 'Ryan Thompson', department: 'Computer Science', year: 'Junior', problems: 298, rating: 1598, streak: 29, avatar: 'https://randomuser.me/api/portraits/men/45.jpg' },
            { id: 8, name: 'Amanda Lee', department: 'Computer Engineering', year: 'Sophomore', problems: 287, rating: 1567, streak: 41, avatar: 'https://randomuser.me/api/portraits/women/67.jpg' }
        ];

        this.renderLeaderboard();
    }

    filterLeaderboard() {
        const departmentFilter = document.querySelector('select');
        const yearFilter = document.querySelectorAll('select')[1];

        let filteredData = [...this.leaderboardData];

        if (departmentFilter && departmentFilter.value && departmentFilter.value !== 'All Departments') {
            filteredData = filteredData.filter(user => user.department === departmentFilter.value);
        }

        if (yearFilter && yearFilter.value && yearFilter.value !== 'All Years') {
            filteredData = filteredData.filter(user => user.year === yearFilter.value);
        }

        this.renderLeaderboard(filteredData);
    }

    renderLeaderboard(data = this.leaderboardData) {
        const tbody = document.querySelector('#leaderboard-section tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        data.forEach((user, index) => {
            const row = document.createElement('tr');
            const rankBg = index === 0 ? 'bg-red-100 text-red-600' : index === 1 ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600';

            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 ${rankBg} rounded-full flex items-center justify-center">
                            <span class="font-bold">${index + 1}</span>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <img class="h-10 w-10 rounded-full" src="${user.avatar}" alt="">
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${user.name}</div>
                            <div class="text-sm text-gray-500">${user.year}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900">${user.department}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 font-bold">${user.problems}</div>
                    <div class="flex mt-1">
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+${Math.floor(Math.random() * 20)}</span>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 font-bold">${user.rating}</div>
                    <div class="flex mt-1">
                        <span class="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded">+${Math.floor(Math.random() * 30)}</span>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <i class="fas fa-fire text-red-500 mr-1"></i>
                        <span class="text-sm text-gray-900 font-bold">${user.streak}</span>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <a href="#" class="text-blue-600 hover:text-blue-900" onclick="app.viewProfile(${user.id})">View</a>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    viewProfile(userId) {
        const user = this.leaderboardData.find(u => u.id === userId);
        if (!user) return;

        const modal = this.createModal(`${user.name}'s Profile`, `
            <div class="space-y-4">
                <div class="flex items-center space-x-4">
                    <img class="h-16 w-16 rounded-full" src="${user.avatar}" alt="">
                    <div>
                        <h3 class="text-lg font-medium">${user.name}</h3>
                        <p class="text-gray-600">${user.department} - ${user.year}</p>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm text-gray-500">Problems Solved</p>
                        <p class="text-xl font-bold text-blue-600">${user.problems}</p>
                    </div>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm text-gray-500">Contest Rating</p>
                        <p class="text-xl font-bold text-red-600">${user.rating}</p>
                    </div>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm text-gray-500">Current Streak</p>
                        <p class="text-xl font-bold text-orange-600">${user.streak} days</p>
                    </div>
                    <div class="bg-gray-50 p-3 rounded">
                        <p class="text-sm text-gray-500">Rank</p>
                        <p class="text-xl font-bold text-purple-600">#${this.leaderboardData.indexOf(user) + 1}</p>
                    </div>
                </div>
                <div class="mt-4">
                    <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700" onclick="app.challengeUser(${user.id})">
                        Challenge to a Duel
                    </button>
                </div>
            </div>
        `);
    }

    challengeUser(userId) {
        const user = this.leaderboardData.find(u => u.id === userId);
        if (!user) return;

        this.showNotification(`Challenge sent to ${user.name}!`, 'success');
        document.querySelector('.fixed').remove(); // Close modal
    }

    // Events functionality
    loadEventsData() {
        this.events = [
            {
                id: 1,
                title: 'Monthly Coding Challenge',
                description: 'Solve 50 problems of any difficulty this month to earn the Coding Champion badge and a chance to win prizes.',
                startDate: '2023-06-01',
                endDate: '2023-06-30',
                participants: 87,
                type: 'challenge',
                registered: false
            },
            {
                id: 2,
                title: '30-Day Streak Challenge',
                description: 'Solve at least one problem every day for 30 days straight to earn the Fire Keeper badge and special recognition.',
                startDate: '2023-06-15',
                endDate: '2023-07-14',
                participants: 64,
                type: 'streak',
                registered: false
            },
            {
                id: 3,
                title: 'Department Showdown',
                description: 'Compete to solve the most problems as a department. The winning department gets bragging rights and a pizza party!',
                startDate: '2023-07-01',
                endDate: '2023-07-31',
                participants: 156,
                type: 'competition',
                registered: false
            }
        ];

        this.updateEventCountdowns();
    }

    registerForEvent(eventTitle) {
        if (!this.currentUser) {
            this.showNotification('Please sign in to register for events!', 'error');
            return;
        }

        const event = this.events.find(e => e.title === eventTitle);
        if (event) {
            event.registered = true;
            event.participants++;
            this.showNotification(`Successfully registered for ${eventTitle}!`, 'success');
            this.earnBadge('Event Participant', `Registered for ${eventTitle}`);
        }
    }

    updateEventCountdowns() {
        setInterval(() => {
            this.events.forEach(event => {
                const startDate = new Date(event.startDate);
                const endDate = new Date(event.endDate);
                const now = new Date();

                let timeLeft;
                let status;

                if (now < startDate) {
                    timeLeft = startDate - now;
                    status = 'Starts in';
                } else if (now < endDate) {
                    timeLeft = endDate - now;
                    status = 'Ends in';
                } else {
                    status = 'Ended';
                    timeLeft = 0;
                }

                if (timeLeft > 0) {
                    const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

                    // Update countdown in UI if element exists
                    const countdownElement = document.querySelector(`[data-event-id="${event.id}"] .countdown`);
                    if (countdownElement) {
                        countdownElement.textContent = `${status}: ${days}d ${hours}h ${minutes}m`;
                    }
                }
            });
        }, 60000); // Update every minute
    }

    // Profile management
    showEditProfileModal() {
        if (!this.currentUser) {
            this.showNotification('Please sign in first!', 'error');
            return;
        }

        const modal = this.createModal('Edit Profile', `
            <form id="edit-profile-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Name</label>
                    <input type="text" value="${this.currentUser.name}" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Department</label>
                    <select class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="Computer Science" ${this.currentUser.department === 'Computer Science' ? 'selected' : ''}>Computer Science</option>
                        <option value="Data Science" ${this.currentUser.department === 'Data Science' ? 'selected' : ''}>Data Science</option>
                        <option value="Computer Engineering" ${this.currentUser.department === 'Computer Engineering' ? 'selected' : ''}>Computer Engineering</option>
                        <option value="Electrical Engineering" ${this.currentUser.department === 'Electrical Engineering' ? 'selected' : ''}>Electrical Engineering</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Year</label>
                    <select class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="Freshman" ${this.currentUser.year === 'Freshman' ? 'selected' : ''}>Freshman</option>
                        <option value="Sophomore" ${this.currentUser.year === 'Sophomore' ? 'selected' : ''}>Sophomore</option>
                        <option value="Junior" ${this.currentUser.year === 'Junior' ? 'selected' : ''}>Junior</option>
                        <option value="Senior" ${this.currentUser.year === 'Senior' ? 'selected' : ''}>Senior</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">LeetCode Username</label>
                    <div class="flex">
                        <input type="text" value="${this.currentUser.leetcodeUsername}" class="mt-1 block w-full border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <button type="button" onclick="app.verifyLeetCodeUsername()" class="mt-1 bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700">
                            Verify
                        </button>
                    </div>
                </div>
                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                    Save Changes
                </button>
            </form>
        `);

        document.getElementById('edit-profile-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleProfileUpdate(e.target);
        });
    }

    handleProfileUpdate(form) {
        const inputs = form.querySelectorAll('input, select');

        inputs.forEach(input => {
            const label = input.previousElementSibling.textContent.toLowerCase();
            if (label.includes('name')) this.currentUser.name = input.value;
            if (label.includes('department')) this.currentUser.department = input.value;
            if (label.includes('year')) this.currentUser.year = input.value;
            if (label.includes('leetcode')) this.currentUser.leetcodeUsername = input.value;
        });

        this.saveUserData();
        this.updateUIForLoggedInUser();
        form.closest('.fixed').remove();
        this.showNotification('Profile updated successfully!', 'success');
    }

    async verifyLeetCodeUsername() {
        const usernameInput = document.querySelector('input[type="text"]');
        const username = usernameInput ? usernameInput.value.trim() : this.currentUser?.leetcodeUsername;

        if (!username) {
            this.showNotification('Please enter a LeetCode username!', 'error');
            return;
        }

        try {
            this.showNotification('Verifying LeetCode username...', 'info');

            // Try to connect to local API server first
            const response = await fetch(`http://localhost:5000/api/verify/${username}?platform=leetcode`);

            if (response.ok) {
                const data = await response.json();

                if (data.success && data.valid) {
                    this.showNotification('LeetCode username verified successfully!', 'success');
                    this.earnBadge('Verified User', 'LeetCode account verified');

                    // Fetch and update user stats
                    this.fetchUserStats(username);
                } else {
                    this.showNotification(data.error || 'Username not found on LeetCode', 'error');
                }
            } else {
                throw new Error('API server not available');
            }
        } catch (error) {
            console.log('API server not available, using simulation');
            // Fallback to simulation if API server is not running
            setTimeout(() => {
                this.showNotification('LeetCode username verified successfully! (Simulated)', 'success');
                this.earnBadge('Verified User', 'LeetCode account verified');
            }, 1500);
        }
    }

    async fetchUserStats(username, platform = 'leetcode') {
        /**
         * Fetch real user statistics from the API server
         */
        try {
            const response = await fetch(`http://localhost:5000/api/${platform}/${username}`);

            if (response.ok) {
                const result = await response.json();

                if (result.success) {
                    const stats = result.data;

                    // Update current user with real data
                    if (this.currentUser) {
                        this.currentUser.problemsSolved = stats.problems_solved?.total || 0;
                        this.currentUser.contestRating = stats.contest_rating || 0;
                        this.currentUser.streak = stats.current_streak || 0;
                        this.currentUser.badges = stats.badges || [];

                        // Update UI with real data
                        this.updateProfileSection();
                        this.saveUserData();

                        this.showNotification(`Real stats loaded for ${username}!`, 'success');

                        // Check for new achievements based on real data
                        this.checkRealAchievements(stats);
                    }
                } else {
                    this.showNotification('Failed to fetch user stats', 'error');
                }
            } else {
                throw new Error('Failed to fetch stats');
            }
        } catch (error) {
            console.log('Could not fetch real stats:', error);
            this.showNotification('Using simulated data (API server not available)', 'info');
        }
    }

    checkRealAchievements(stats) {
        /**
         * Check achievements based on real LeetCode data
         */
        if (!stats.problems_solved) return;

        const { total, easy, medium, hard } = stats.problems_solved;
        const currentStreak = stats.current_streak || 0;
        const highestStreak = stats.highest_streak || 0;

        // Problem-based achievements
        const problemMilestones = [
            { count: 1, name: 'First Problem', desc: 'Solved your first problem!' },
            { count: 10, name: 'Getting Started', desc: 'Solved 10 problems!' },
            { count: 50, name: 'Problem Solver', desc: 'Solved 50 problems!' },
            { count: 100, name: 'Coding Enthusiast', desc: 'Solved 100 problems!' },
            { count: 200, name: 'Algorithm Master', desc: 'Solved 200 problems!' },
            { count: 500, name: 'Coding Legend', desc: 'Solved 500 problems!' }
        ];

        problemMilestones.forEach(milestone => {
            if (total >= milestone.count) {
                if (!this.currentUser.badges || !this.currentUser.badges.find(b => b.name === milestone.name)) {
                    this.earnBadge(milestone.name, milestone.desc);
                }
            }
        });

        // Difficulty-based achievements
        if (hard >= 1) this.earnBadge('Hard Problem Solver', 'Solved your first hard problem!');
        if (hard >= 10) this.earnBadge('Hard Problem Master', 'Solved 10 hard problems!');
        if (medium >= 50) this.earnBadge('Medium Problem Expert', 'Solved 50 medium problems!');
        if (easy >= 100) this.earnBadge('Easy Problem Champion', 'Solved 100 easy problems!');

        // Streak-based achievements
        if (currentStreak >= 7) this.earnBadge('Week Warrior', '7-day streak achieved!');
        if (currentStreak >= 30) this.earnBadge('Month Master', '30-day streak achieved!');
        if (highestStreak >= 100) this.earnBadge('Streak Legend', '100-day streak achieved!');
    }

    // Charts and Analytics
    setupCharts() {
        // Load Chart.js if not already loaded
        if (typeof Chart === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            script.onload = () => this.initializeCharts();
            document.head.appendChild(script);
        } else {
            this.initializeCharts();
        }
    }

    initializeCharts() {
        this.createProblemsOverTimeChart();
        this.createDifficultyDistributionChart();
        this.createDepartmentComparisonChart();
    }

    createProblemsOverTimeChart() {
        const ctx = document.createElement('canvas');
        const container = document.querySelector('#analytics-section .h-64');
        if (container) {
            container.innerHTML = '';
            container.appendChild(ctx);

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Problems Solved',
                        data: [1200, 1350, 1500, 1680, 1850, 2100],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    createDifficultyDistributionChart() {
        const containers = document.querySelectorAll('#analytics-section .h-64');
        if (containers.length > 1) {
            const ctx = document.createElement('canvas');
            containers[1].innerHTML = '';
            containers[1].appendChild(ctx);

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Easy', 'Medium', 'Hard'],
                    datasets: [{
                        data: [7542, 4321, 984],
                        backgroundColor: [
                            'rgb(34, 197, 94)',
                            'rgb(234, 179, 8)',
                            'rgb(239, 68, 68)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    createDepartmentComparisonChart() {
        const containers = document.querySelectorAll('#analytics-section .h-80');
        if (containers.length > 0) {
            const ctx = document.createElement('canvas');
            containers[0].innerHTML = '';
            containers[0].appendChild(ctx);

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Computer Science', 'Data Science', 'Computer Eng.', 'Electrical Eng.'],
                    datasets: [{
                        label: 'Problems Solved',
                        data: [5428, 3721, 2156, 1542],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: 'rgb(59, 130, 246)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    // Badge and Achievement System
    earnBadge(badgeName, description) {
        const badge = document.createElement('div');
        badge.className = 'fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50';
        badge.innerHTML = `
            <div class="bg-white p-8 rounded-xl shadow-2xl max-w-sm text-center transform transition-all duration-500 scale-0">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
                    <i class="fas fa-trophy text-yellow-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Badge Earned!</h3>
                <h4 class="text-md font-semibold text-blue-600 mb-2">${badgeName}</h4>
                <p class="text-sm text-gray-500 mb-4">${description}</p>
                <button onclick="this.parentElement.parentElement.remove(); app.createConfetti()" class="bg-blue-600 text-white px-6 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                    Awesome!
                </button>
            </div>
        `;

        document.body.appendChild(badge);

        setTimeout(() => {
            badge.querySelector('div').classList.add('scale-100');
            badge.querySelector('div').classList.remove('scale-0');
        }, 100);

        // Add badge to user's collection
        if (this.currentUser) {
            if (!this.currentUser.badges) this.currentUser.badges = [];
            this.currentUser.badges.push({
                name: badgeName,
                description: description,
                earnedAt: new Date().toISOString()
            });
            this.saveUserData();
        }
    }

    createConfetti() {
        for (let i = 0; i < 50; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'fixed w-2 h-2 pointer-events-none z-40';
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.top = '-10px';
            confetti.style.backgroundColor = ['#f00', '#0f0', '#00f', '#ff0', '#f0f', '#0ff'][Math.floor(Math.random() * 6)];
            confetti.style.animation = `confetti-fall ${Math.random() * 3 + 2}s linear forwards`;
            document.body.appendChild(confetti);

            setTimeout(() => confetti.remove(), 5000);
        }
    }

    // Real-time features
    startRealTimeUpdates() {
        // Simulate real-time leaderboard updates
        setInterval(() => {
            this.simulateLeaderboardUpdate();
        }, 30000); // Update every 30 seconds

        // Simulate streak updates
        setInterval(() => {
            this.updateStreakCounters();
        }, 60000); // Update every minute

        // Check for achievements
        setInterval(() => {
            this.checkAchievements();
        }, 10000); // Check every 10 seconds
    }

    simulateLeaderboardUpdate() {
        if (this.leaderboardData.length > 0) {
            const randomUser = this.leaderboardData[Math.floor(Math.random() * this.leaderboardData.length)];
            const oldProblems = randomUser.problems;
            randomUser.problems += Math.floor(Math.random() * 3) + 1;

            this.renderLeaderboard();

            if (this.currentUser && randomUser.name !== this.currentUser.name) {
                this.showNotification(`${randomUser.name} solved ${randomUser.problems - oldProblems} new problem(s)!`, 'info');
            }
        }
    }

    updateStreakCounters() {
        // Update streak animations
        document.querySelectorAll('.streak-flame').forEach(flame => {
            flame.style.animation = 'none';
            setTimeout(() => {
                flame.style.animation = 'pulse 1.5s infinite';
            }, 10);
        });
    }

    checkAchievements() {
        if (!this.currentUser) return;

        // Check for milestone achievements
        const milestones = [
            { problems: 50, badge: 'Problem Solver', description: 'Solved 50 problems!' },
            { problems: 100, badge: 'Coding Enthusiast', description: 'Solved 100 problems!' },
            { problems: 200, badge: 'Algorithm Master', description: 'Solved 200 problems!' },
            { streak: 7, badge: 'Week Warrior', description: '7-day streak achieved!' },
            { streak: 30, badge: 'Month Master', description: '30-day streak achieved!' }
        ];

        milestones.forEach(milestone => {
            if (milestone.problems && this.currentUser.problemsSolved >= milestone.problems) {
                if (!this.currentUser.badges || !this.currentUser.badges.find(b => b.name === milestone.badge)) {
                    this.earnBadge(milestone.badge, milestone.description);
                }
            }
            if (milestone.streak && this.currentUser.streak >= milestone.streak) {
                if (!this.currentUser.badges || !this.currentUser.badges.find(b => b.name === milestone.badge)) {
                    this.earnBadge(milestone.badge, milestone.description);
                }
            }
        });
    }

    // Search functionality
    setupSearch() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = 'Search students...';
        searchInput.className = 'mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md';

        const leaderboardHeader = document.querySelector('#leaderboard-section .lg\\:flex');
        if (leaderboardHeader) {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'mt-5 lg:mt-0 lg:ml-4';
            searchContainer.appendChild(searchInput);
            leaderboardHeader.appendChild(searchContainer);

            searchInput.addEventListener('input', (e) => {
                this.searchLeaderboard(e.target.value);
            });
        }
    }

    searchLeaderboard(query) {
        if (!query.trim()) {
            this.renderLeaderboard();
            return;
        }

        const filteredData = this.leaderboardData.filter(user =>
            user.name.toLowerCase().includes(query.toLowerCase()) ||
            user.department.toLowerCase().includes(query.toLowerCase())
        );

        this.renderLeaderboard(filteredData);
    }

    async syncUserDataWithAPI() {
        /**
         * Sync user data with the Python API server
         */
        if (!this.currentUser || !this.currentUser.leetcodeUsername) {
            return;
        }

        try {
            const username = this.currentUser.leetcodeUsername;

            // Fetch comprehensive stats
            const response = await fetch(`http://localhost:5000/api/leetcode/${username}`);

            if (response.ok) {
                const result = await response.json();

                if (result.success) {
                    const stats = result.data;

                    // Update user data with real API data
                    this.currentUser.problemsSolved = stats.problems_solved?.total || 0;
                    this.currentUser.contestRating = stats.contest_rating || 0;
                    this.currentUser.streak = stats.current_streak || 0;
                    this.currentUser.highestStreak = stats.highest_streak || 0;
                    this.currentUser.totalActiveDays = stats.total_active_days || 0;
                    this.currentUser.lastUpdated = new Date().toISOString();

                    // Update difficulty breakdown
                    if (stats.problems_solved) {
                        this.currentUser.easyProblems = stats.problems_solved.easy || 0;
                        this.currentUser.mediumProblems = stats.problems_solved.medium || 0;
                        this.currentUser.hardProblems = stats.problems_solved.hard || 0;
                    }

                    // Save updated data
                    this.saveUserData();
                    this.updateProfileSection();

                    // Update leaderboard if user is in it
                    this.updateUserInLeaderboard();

                    console.log('User data synced with API successfully');
                    return true;
                }
            }
        } catch (error) {
            console.log('API sync failed, using cached data:', error);
        }

        return false;
    }

    updateUserInLeaderboard() {
        /**
         * Update current user's position in the leaderboard
         */
        if (!this.currentUser) return;

        const userIndex = this.leaderboardData.findIndex(user =>
            user.name === this.currentUser.name
        );

        if (userIndex !== -1) {
            // Update existing user in leaderboard
            this.leaderboardData[userIndex].problems = this.currentUser.problemsSolved;
            this.leaderboardData[userIndex].rating = this.currentUser.contestRating;
            this.leaderboardData[userIndex].streak = this.currentUser.streak;
        } else {
            // Add user to leaderboard
            this.leaderboardData.push({
                id: Date.now(),
                name: this.currentUser.name,
                department: this.currentUser.department,
                year: this.currentUser.year,
                problems: this.currentUser.problemsSolved,
                rating: this.currentUser.contestRating,
                streak: this.currentUser.streak,
                avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
            });
        }

        // Re-sort leaderboard by problems solved
        this.leaderboardData.sort((a, b) => b.problems - a.problems);

        // Update user's rank
        const newRank = this.leaderboardData.findIndex(user =>
            user.name === this.currentUser.name
        ) + 1;

        if (newRank !== this.currentUser.rank) {
            const oldRank = this.currentUser.rank;
            this.currentUser.rank = newRank;

            if (oldRank && newRank < oldRank) {
                this.showNotification(`🎉 You moved up to rank #${newRank}!`, 'success');
            }
        }

        this.renderLeaderboard();
    }

    // User menu functionality
    showUserMenu(event) {
        const menu = document.createElement('div');
        menu.className = 'absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50 border';
        menu.innerHTML = `
            <div class="py-1">
                <a href="#profile-section" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">View Profile</a>
                <a href="#" onclick="app.showEditProfileModal()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Edit Profile</a>
                <a href="#" onclick="app.syncUserDataWithAPI()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">🔄 Sync LeetCode Data</a>
                <a href="#" onclick="app.showAchievements()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">My Achievements</a>
                <a href="#" onclick="app.showSettings()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
                <div class="border-t border-gray-100"></div>
                <a href="#" onclick="app.signOut()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign Out</a>
            </div>
        `;

        // Position menu relative to button
        const button = event.target;
        const rect = button.getBoundingClientRect();
        menu.style.position = 'fixed';
        menu.style.top = rect.bottom + 'px';
        menu.style.right = (window.innerWidth - rect.right) + 'px';

        document.body.appendChild(menu);

        // Close menu when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                if (!menu.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        }, 100);
    }

    showAchievements() {
        const badges = this.currentUser?.badges || [];
        const modal = this.createModal('My Achievements', `
            <div class="space-y-4">
                ${badges.length === 0 ?
                    '<p class="text-gray-500 text-center">No badges earned yet. Keep coding to unlock achievements!</p>' :
                    badges.map(badge => `
                        <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-trophy text-yellow-600"></i>
                            </div>
                            <div>
                                <h4 class="font-medium">${badge.name}</h4>
                                <p class="text-sm text-gray-600">${badge.description}</p>
                                <p class="text-xs text-gray-400">Earned: ${new Date(badge.earnedAt).toLocaleDateString()}</p>
                            </div>
                        </div>
                    `).join('')
                }
            </div>
        `);
    }

    showSettings() {
        const modal = this.createModal('Settings', `
            <div class="space-y-4">
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                        <span class="ml-2 text-sm text-gray-700">Email notifications</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                        <span class="ml-2 text-sm text-gray-700">Push notifications</span>
                    </label>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Dark mode</span>
                    </label>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Preferred Language</label>
                    <select class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option>JavaScript</option>
                        <option>Python</option>
                        <option>Java</option>
                        <option>C++</option>
                    </select>
                </div>
                <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                    Save Settings
                </button>
            </div>
        `);
    }

    signOut() {
        this.currentUser = null;
        localStorage.removeItem('coderank_user');
        location.reload();
    }
}

// Initialize the application when DOM is ready
let app;

// Add confetti animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes confetti-fall {
        0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
        100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize app when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        app = new CodeRankApp();
        console.log('✅ CodeRank app initialized after DOM loaded');
    });
} else {
    // DOM is already loaded
    app = new CodeRankApp();
    console.log('✅ CodeRank app initialized immediately');
}
