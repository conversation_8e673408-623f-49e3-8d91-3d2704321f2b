<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Auth Test - CodeRank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <div class="bg-blue-600 text-white p-4">
        <div class="max-w-4xl mx-auto flex justify-between items-center">
            <h1 class="text-xl font-bold">
                <i class="fas fa-code mr-2"></i>
                CodeRank - Simple Auth Test
            </h1>
            <div class="space-x-2">
                <button id="signin-btn" class="bg-blue-800 hover:bg-blue-900 px-4 py-2 rounded text-sm">
                    Sign In
                </button>
                <button id="register-btn" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded text-sm">
                    Register
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-8 px-4">
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-2xl font-bold mb-4">🔐 Authentication Test</h2>
            <p class="text-gray-600 mb-6">Click the buttons above to test Sign In and Register functionality.</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-bold mb-2">Test Buttons:</h3>
                    <div class="space-y-2">
                        <button onclick="showSignIn()" class="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                            Test Sign In Modal
                        </button>
                        <button onclick="showRegister()" class="w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600">
                            Test Register Modal
                        </button>
                        <button onclick="testBasic()" class="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                            Test Basic Modal
                        </button>
                    </div>
                </div>
                
                <div>
                    <h3 class="font-bold mb-2">Status:</h3>
                    <div id="status" class="bg-gray-100 p-4 rounded text-sm">
                        Ready to test...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple modal creation function
        function createModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    ${content}
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
            
            return modal;
        }

        // Sign In Modal
        function showSignIn() {
            updateStatus('Opening Sign In modal...');
            
            const content = `
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-500">Forgot password?</a>
                    </div>
                    <button type="submit" onclick="handleSignIn(event)" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Sign In
                    </button>
                </form>
            `;
            
            createModal('Sign In', content);
            updateStatus('✅ Sign In modal opened successfully!');
        }

        // Register Modal
        function showRegister() {
            updateStatus('Opening Register modal...');
            
            const content = `
                <form class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">First Name</label>
                            <input type="text" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Last Name</label>
                            <input type="text" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Department</label>
                        <select required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Department</option>
                            <option value="Computer Science">Computer Science</option>
                            <option value="Data Science">Data Science</option>
                            <option value="Computer Engineering">Computer Engineering</option>
                            <option value="Electrical Engineering">Electrical Engineering</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Year</label>
                        <select required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select Year</option>
                            <option value="Freshman">Freshman</option>
                            <option value="Sophomore">Sophomore</option>
                            <option value="Junior">Junior</option>
                            <option value="Senior">Senior</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">LeetCode Username</label>
                        <div class="flex">
                            <input type="text" required class="mt-1 block w-full border border-gray-300 rounded-l-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="your-leetcode-username">
                            <button type="button" onclick="verifyLeetCode()" class="mt-1 bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700">
                                Verify
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Confirm Password</label>
                        <input type="password" required class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <button type="submit" onclick="handleRegister(event)" class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        Register
                    </button>
                </form>
            `;
            
            createModal('Register', content);
            updateStatus('✅ Register modal opened successfully!');
        }

        // Basic test modal
        function testBasic() {
            updateStatus('Testing basic modal...');
            
            const content = `
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                        <i class="fas fa-check text-green-600 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Test Successful!</h3>
                    <p class="text-sm text-gray-500 mb-4">Basic modal functionality is working correctly.</p>
                    <button onclick="this.closest('.fixed').remove()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                        Close
                    </button>
                </div>
            `;
            
            createModal('Test Modal', content);
            updateStatus('✅ Basic modal test completed!');
        }

        // Handle form submissions
        function handleSignIn(event) {
            event.preventDefault();
            updateStatus('✅ Sign In form submitted successfully!');
            showNotification('Welcome back! Sign in successful.', 'success');
            event.target.closest('.fixed').remove();
        }

        function handleRegister(event) {
            event.preventDefault();
            updateStatus('✅ Registration form submitted successfully!');
            showNotification('Welcome to CodeRank! Registration successful.', 'success');
            event.target.closest('.fixed').remove();
        }

        function verifyLeetCode() {
            updateStatus('🔄 Verifying LeetCode username...');
            setTimeout(() => {
                updateStatus('✅ LeetCode username verified!');
                showNotification('LeetCode username verified successfully!', 'success');
            }, 1500);
        }

        // Update status function
        function updateStatus(message) {
            document.getElementById('status').innerHTML = `
                <div class="text-sm">
                    <span class="font-medium">${new Date().toLocaleTimeString()}</span>: ${message}
                </div>
            `;
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg max-w-sm transform transition-all duration-300`;
            
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
            notification.classList.add(bgColor, 'text-white');
            
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation' : 'info'}-circle mr-2"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 5000);
        }

        // Set up header buttons
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('signin-btn').addEventListener('click', showSignIn);
            document.getElementById('register-btn').addEventListener('click', showRegister);
            updateStatus('Page loaded and ready for testing!');
        });
    </script>
</body>
</html>
