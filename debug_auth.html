<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Authentication - CodeRank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Debug Header -->
    <div class="bg-red-600 text-white p-4">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-xl font-bold">🐛 Authentication Debug Page</h1>
            <p class="text-red-100">Testing Sign In and Register functionality</p>
        </div>
    </div>

    <!-- Navigation (Simplified) -->
    <nav class="bg-white shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-code text-red-600 text-2xl mr-2"></i>
                        <span class="text-xl font-bold text-blue-800">CodeRank Debug</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Test Buttons -->
                    <button id="test-signin-1" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Test Sign In (Method 1)
                    </button>
                    <button id="test-register-1" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Test Register (Method 1)
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Debug Content -->
    <div class="max-w-4xl mx-auto py-8 px-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Test Methods -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-lg font-bold mb-4">🧪 Test Methods</h2>
                <div class="space-y-3">
                    <button id="test-signin-2" class="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                        Direct Function Call - Sign In
                    </button>
                    <button id="test-register-2" class="w-full bg-red-500 text-white py-2 px-4 rounded hover:bg-red-600">
                        Direct Function Call - Register
                    </button>
                    <button id="test-modal" class="w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
                        Test Modal Creation
                    </button>
                    <button id="test-app" class="w-full bg-purple-500 text-white py-2 px-4 rounded hover:bg-purple-600">
                        Check App Status
                    </button>
                </div>
            </div>

            <!-- Debug Log -->
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-lg font-bold mb-4">📋 Debug Log</h2>
                <div id="debug-log" class="bg-gray-100 p-4 rounded text-sm font-mono h-64 overflow-y-auto">
                    Initializing debug session...<br>
                </div>
                <button id="clear-log" class="mt-2 bg-gray-500 text-white px-3 py-1 rounded text-sm">
                    Clear Log
                </button>
            </div>
        </div>

        <!-- Manual Test -->
        <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 class="text-lg font-bold text-yellow-800 mb-2">🔧 Manual Test</h3>
            <p class="text-yellow-700 mb-4">If the buttons above don't work, try these manual tests:</p>
            <div class="space-y-2">
                <button onclick="alert('Basic JavaScript works!')" class="bg-yellow-500 text-white px-4 py-2 rounded mr-2">
                    Test Basic JS
                </button>
                <button onclick="testManualModal()" class="bg-yellow-600 text-white px-4 py-2 rounded mr-2">
                    Test Manual Modal
                </button>
                <button onclick="console.log('Console test:', typeof app, app)" class="bg-yellow-700 text-white px-4 py-2 rounded">
                    Log App Status
                </button>
            </div>
        </div>
    </div>

    <!-- Load the main app -->
    <script src="app.js"></script>
    
    <script>
        // Debug logging function
        function debugLog(message) {
            const log = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        // Clear log function
        document.getElementById('clear-log').addEventListener('click', function() {
            document.getElementById('debug-log').innerHTML = 'Log cleared...<br>';
        });

        // Manual modal test
        function testManualModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
            modal.innerHTML = `
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                        <h3 class="text-lg font-medium text-gray-900">Manual Test Modal</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500">This modal was created manually to test if basic modal functionality works.</p>
                        </div>
                        <div class="items-center px-4 py-3">
                            <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-600">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            debugLog('Manual modal created successfully');
        }

        // Wait for DOM and app
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM Content Loaded');
            
            // Check if app exists
            function checkApp() {
                if (typeof app !== 'undefined' && app) {
                    debugLog('✅ App found and initialized');
                    debugLog(`App methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(app)).join(', ')}`);
                    setupTests();
                } else {
                    debugLog('⏳ App not ready yet, checking again...');
                    setTimeout(checkApp, 100);
                }
            }
            
            checkApp();
        });

        function setupTests() {
            debugLog('Setting up test buttons...');

            // Test Sign In - Method 1 (onclick attribute)
            document.getElementById('test-signin-1').addEventListener('click', function() {
                debugLog('Test Sign In Method 1 clicked');
                try {
                    if (app && app.showSignInModal) {
                        app.showSignInModal();
                        debugLog('✅ Sign In modal called successfully');
                    } else {
                        debugLog('❌ app.showSignInModal not found');
                    }
                } catch (error) {
                    debugLog(`❌ Error: ${error.message}`);
                }
            });

            // Test Register - Method 1
            document.getElementById('test-register-1').addEventListener('click', function() {
                debugLog('Test Register Method 1 clicked');
                try {
                    if (app && app.showRegisterModal) {
                        app.showRegisterModal();
                        debugLog('✅ Register modal called successfully');
                    } else {
                        debugLog('❌ app.showRegisterModal not found');
                    }
                } catch (error) {
                    debugLog(`❌ Error: ${error.message}`);
                }
            });

            // Test Sign In - Method 2 (direct call)
            document.getElementById('test-signin-2').addEventListener('click', function() {
                debugLog('Test Sign In Method 2 - Direct call');
                try {
                    app.showSignInModal();
                    debugLog('✅ Direct sign in call successful');
                } catch (error) {
                    debugLog(`❌ Direct call error: ${error.message}`);
                }
            });

            // Test Register - Method 2 (direct call)
            document.getElementById('test-register-2').addEventListener('click', function() {
                debugLog('Test Register Method 2 - Direct call');
                try {
                    app.showRegisterModal();
                    debugLog('✅ Direct register call successful');
                } catch (error) {
                    debugLog(`❌ Direct call error: ${error.message}`);
                }
            });

            // Test Modal Creation
            document.getElementById('test-modal').addEventListener('click', function() {
                debugLog('Testing modal creation function');
                try {
                    if (app && app.createModal) {
                        const modal = app.createModal('Test Modal', '<p>This is a test modal created by the app.createModal function.</p>');
                        debugLog('✅ Modal creation successful');
                    } else {
                        debugLog('❌ app.createModal not found');
                    }
                } catch (error) {
                    debugLog(`❌ Modal creation error: ${error.message}`);
                }
            });

            // Check App Status
            document.getElementById('test-app').addEventListener('click', function() {
                debugLog('=== APP STATUS CHECK ===');
                debugLog(`typeof app: ${typeof app}`);
                debugLog(`app exists: ${!!app}`);
                if (app) {
                    debugLog(`app constructor: ${app.constructor.name}`);
                    debugLog(`app.currentUser: ${JSON.stringify(app.currentUser)}`);
                    debugLog(`showSignInModal exists: ${typeof app.showSignInModal}`);
                    debugLog(`showRegisterModal exists: ${typeof app.showRegisterModal}`);
                    debugLog(`createModal exists: ${typeof app.createModal}`);
                }
                debugLog('=== END STATUS CHECK ===');
            });

            debugLog('✅ All test buttons set up successfully');
        }
    </script>
</body>
</html>
